{"$schema": "https://cdn.statically.io/gh/nguyenngoclongdev/cdn/main/schema/v10/terminal-keeper.json", "theme": "tribe", "active": "default", "activateOnStartup": true, "keepExistingTerminals": false, "sessions": {"default": [{"name": "⚙️ General CLI / Root", "autoExecuteCommands": true, "icon": "terminal-cmd", "color": "terminal.ansiMagenta", "commands": ["echo '🎯 FitRewards Platform - Development Ready'", "echo '✅ Convex authentication is now automated!'", "echo ''", "echo '🚀 Quick Commands:'", "echo '   pnpm dev        # Start both servers in parallel'", "echo '   pnpm dev:web    # Start only frontend'", "echo '   pnpm dev:db     # Start only backend'", "echo '   pnpm build      # Build all packages'", "echo '   pnpm lint       # Lint all packages'"], "focus": true}, {"name": "🌐 Frontend (Vite)", "autoExecuteCommands": true, "icon": "globe", "color": "terminal.ansiCyan", "commands": ["echo '🌐 Starting Frontend Server...'", "pnpm --filter @fitness-rewards/web dev"]}, {"name": "📦 Backend (Convex) - Automated", "autoExecuteCommands": true, "icon": "database", "color": "terminal.ansiGreen", "commands": ["echo '📦 Starting Convex Backend (No Auth Needed)...'", "pnpm --filter @fitness-rewards/db dev"]}, {"name": "🚀 Convex Operations", "autoExecuteCommands": true, "icon": "rocket", "color": "terminal.ansi<PERSON><PERSON>w", "commands": ["echo '🚀 Convex Operations Terminal'", "echo '━━━━━━━━━━━━━━━━━━━━━━━━━━━━'", "echo ''", "echo '🌱 Data & Development:'", "echo '   pnpm --filter @fitness-rewards/db exec convex run seed # Run the data seeder'", "echo '   pnpm --filter @fitness-rewards/db generate             # Generate types and data model'", "echo ''", "echo '🚢 Deployment:'", "echo '   pnpm --filter @fitness-rewards/db deploy               # Deploy backend to production'", "echo ''", "echo '🔍 Debugging:'", "echo '   pnpm --filter @fitness-rewards/db exec convex logs     # View production logs'", "echo '   pnpm --filter @fitness-rewards/db exec convex dashboard  # Open Convex dashboard'"]}, {"name": "🧪 E2E Testing (Playwright) - DRY & Fast", "autoExecuteCommands": true, "icon": "beaker", "color": "terminal.ansiRed", "commands": ["echo '🎭 Playwright E2E Testing Terminal - DRY & Optimized'", "echo '━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━'", "echo ''", "echo '⚡ FAST Commands (3-5x faster):'", "echo '   pnpm test:e2e:fast                    # Super fast - single browser, parallel'", "echo '   pnpm test:e2e:fast tests/basic-*      # Basic functionality tests (~4s)'", "echo '   pnpm test:e2e:fast tests/client-*     # Activity management tests'", "echo ''", "echo '🎯 Standard Commands:'", "echo '   pnpm test:e2e                         # All browsers, full suite'", "echo '   pnpm test:e2e:ui                      # Interactive UI mode'", "echo '   pnpm test:e2e:headed                  # Watch tests run'", "echo '   pnpm test:e2e:debug                   # Debug mode with breakpoints'", "echo ''", "echo '🔍 Debug Commands:'", "echo '   pnpm test:e2e tests/debug-*           # Debug specific issues'", "echo '   pnpm exec playwright show-trace       # View test traces'", "echo ''", "echo '✅ Features: Global auth, DRY helpers, fast timeouts, parallel execution'", "echo '🎯 Ready for lightning-fast e2e testing...'"]}, {"name": "🔧 Development Tools", "autoExecuteCommands": true, "icon": "tools", "color": "terminal.ansiBlue", "commands": ["echo '🔧 Development Tools Terminal'", "echo '━━━━━━━━━━━━━━━━━━━━━━━━━━'", "echo ''", "echo '📋 Available Commands:'", "echo '   pnpm test          # Run all unit/integration tests'", "echo '   pnpm type-check    # TypeScript validation'", "echo '   pnpm format        # Format code with <PERSON><PERSON><PERSON>'", "echo '   pnpm clean         # Clean all build artifacts'", "echo ''", "echo '🎯 Ready for manual commands...'", "pwd"]}]}}
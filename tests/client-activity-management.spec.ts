import { test, expect } from '@playwright/test';

test.describe('ACT-1: Client Admin CRUD Operations on Activity Types', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/admin');
    await page.getByRole('tab', { name: 'Activities' }).click();
  });

  test.afterEach(async ({ page }) => {
    // Clean up any test activity types created during tests
    await cleanupTestActivityTypes(page);
  });

  test('should display activities management interface with create button', async ({
    page,
  }) => {
    await expect(
      page.getByRole('heading', { name: 'Manage Activities' })
    ).toBeVisible();
    await expect(
      page.getByText('Define the activities members can log to earn points')
    ).toBeVisible();
    await expect(
      page.getByRole('button', { name: 'Create Activity' })
    ).toBeVisible();
  });

  test('should create a new activity type successfully', async ({ page }) => {
    // Click create activity button
    await page.getByRole('button', { name: 'Create Activity' }).click();

    // Verify modal opens
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(
      page.getByRole('heading', { name: 'Create New Activity Type' })
    ).toBeVisible();

    // Fill out the form
    await page.getByLabel('Name').fill('Personal Training Session');
    await page.getByLabel('Points').fill('50');
    await page.getByLabel('Icon Name').fill('dumbbell');

    // Verify key is auto-generated
    await expect(page.getByLabel('Key')).toHaveValue(
      'personal_training_session'
    );

    // Submit the form
    await page.getByRole('button', { name: 'Create' }).click();

    // Verify success toast appears
    await expect(
      page.getByText('Activity type successfully created!')
    ).toBeVisible();

    // Verify modal closes
    await expect(page.getByRole('dialog')).not.toBeVisible();

    // Verify new activity appears in table
    await expect(
      page.getByRole('cell', { name: 'Personal Training Session' })
    ).toBeVisible();
    await expect(page.getByRole('cell', { name: '50' })).toBeVisible();
  });

  test('should validate required fields when creating activity', async ({
    page,
  }) => {
    await page.getByRole('button', { name: 'Create Activity' }).click();

    // Try to submit empty form
    await page.getByRole('button', { name: 'Create' }).click();

    // Verify validation errors
    await expect(
      page.getByText('Name must be at least 3 characters long')
    ).toBeVisible();
    await expect(
      page.getByText('Key must be at least 3 characters long')
    ).toBeVisible();
    await expect(page.getByText('Icon name cannot be empty')).toBeVisible();
  });

  test('should edit an existing activity type', async ({ page }) => {
    // First create an activity to edit
    await createTestActivity(page, 'Test Activity', 25, 'activity');

    // Click edit button for the test activity
    await page
      .getByRole('row', { name: /Test Activity/ })
      .getByRole('button', { name: 'Edit' })
      .click();

    // Verify modal opens with existing data
    await expect(
      page.getByRole('heading', { name: 'Edit Activity Type' })
    ).toBeVisible();
    await expect(page.getByLabel('Name')).toHaveValue('Test Activity');
    await expect(page.getByLabel('Points')).toHaveValue('25');

    // Update the activity
    await page.getByLabel('Name').fill('Updated Test Activity');
    await page.getByLabel('Points').fill('35');

    // Submit changes
    await page.getByRole('button', { name: 'Update' }).click();

    // Verify success toast
    await expect(
      page.getByText('Activity type successfully updated!')
    ).toBeVisible();

    // Verify changes in table
    await expect(
      page.getByRole('cell', { name: 'Updated Test Activity' })
    ).toBeVisible();
    await expect(page.getByRole('cell', { name: '35' })).toBeVisible();
  });

  test('should delete an activity type when not used by milestones', async ({
    page,
  }) => {
    // Create a test activity
    await createTestActivity(page, 'Deletable Activity', 15, 'trash');

    // Click delete button
    await page
      .getByRole('row', { name: /Deletable Activity/ })
      .getByRole('button', { name: 'Delete' })
      .click();

    // Verify success toast
    await expect(
      page.getByText('Activity type deleted successfully!')
    ).toBeVisible();

    // Verify activity is removed from table
    await expect(
      page.getByRole('cell', { name: 'Deletable Activity' })
    ).not.toBeVisible();
  });

  test('should prevent deletion of activity type used by milestones', async ({
    page,
  }) => {
    // This test assumes there's an activity type that's referenced by a milestone
    // We'll use the default "Class Attendance" which should be protected

    // Try to delete the protected activity
    await page
      .getByRole('row', { name: /Class Attendance/ })
      .getByRole('button', { name: 'Delete' })
      .click();

    // Verify error toast appears
    await expect(
      page.getByText(/cannot be deleted.*used by milestones/i)
    ).toBeVisible();

    // Verify activity is still in table
    await expect(
      page.getByRole('cell', { name: 'Class Attendance' })
    ).toBeVisible();
  });

  test('should prevent duplicate activity keys within client', async ({
    page,
  }) => {
    // Create first activity
    await createTestActivity(page, 'Unique Activity', 20, 'star');

    // Try to create another activity with same key
    await page.getByRole('button', { name: 'Create Activity' }).click();
    await page.getByLabel('Name').fill('Another Activity');
    await page.getByLabel('Key').fill('unique_activity'); // Same key as first activity
    await page.getByLabel('Points').fill('30');
    await page.getByLabel('Icon Name').fill('heart');

    await page.getByRole('button', { name: 'Create' }).click();

    // Verify error message
    await expect(
      page.getByText(/Activity type with key.*already exists/i)
    ).toBeVisible();
  });

  test('should display empty state when no activities exist', async ({
    page,
  }) => {
    // This test would need to run in isolation or after clearing all activities
    // For now, we'll check if the table structure exists
    await expect(page.getByRole('table')).toBeVisible();
    await expect(
      page.getByRole('columnheader', { name: 'Name' })
    ).toBeVisible();
    await expect(page.getByRole('columnheader', { name: 'Key' })).toBeVisible();
    await expect(
      page.getByRole('columnheader', { name: 'Points' })
    ).toBeVisible();
    await expect(
      page.getByRole('columnheader', { name: 'Icon' })
    ).toBeVisible();
    await expect(
      page.getByRole('columnheader', { name: 'Actions' })
    ).toBeVisible();
  });

  // Visual snapshot test for the activities management interface
  test('should match visual snapshot of activities management tab', async ({
    page,
  }) => {
    // Wait for the interface to fully load - use domcontentloaded instead of networkidle for speed
    await page.waitForLoadState('domcontentloaded');

    // Take screenshot of the main content area
    await expect(
      page.getByTestId('activities-management-content')
    ).toHaveScreenshot('activities-management-tab.png');
  });
});

test.describe('ACT-2: Activity Point Value Assignment', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/admin');
    await page.getByRole('tab', { name: 'Activities' }).click();
  });

  test.afterEach(async ({ page }) => {
    await cleanupTestActivityTypes(page);
  });

  test('should allow setting custom point values for activities', async ({
    page,
  }) => {
    await page.getByRole('button', { name: 'Create Activity' }).click();

    // Test various point values
    const testCases = [
      { name: 'Low Value Activity', points: '5' },
      { name: 'Medium Value Activity', points: '25' },
      { name: 'High Value Activity', points: '100' },
    ];

    for (const testCase of testCases) {
      await page.getByLabel('Name').fill(testCase.name);
      await page.getByLabel('Points').fill(testCase.points);
      await page.getByLabel('Icon Name').fill('star');

      await page.getByRole('button', { name: 'Create' }).click();

      // Verify success and close modal for next iteration
      await expect(
        page.getByText('Activity type successfully created!')
      ).toBeVisible();

      // Verify points display correctly in table
      await expect(
        page.getByRole('cell', { name: testCase.points })
      ).toBeVisible();

      // Open modal for next iteration (except last one)
      if (testCase !== testCases[testCases.length - 1]) {
        await page.getByRole('button', { name: 'Create Activity' }).click();
      }
    }
  });

  test('should validate that points must be positive numbers', async ({
    page,
  }) => {
    await page.getByRole('button', { name: 'Create Activity' }).click();

    await page.getByLabel('Name').fill('Test Activity');
    await page.getByLabel('Icon Name').fill('activity');

    // Test invalid point values
    const invalidValues = ['0', '-5', 'abc', ''];

    for (const value of invalidValues) {
      await page.getByLabel('Points').fill(value);
      await page.getByRole('button', { name: 'Create' }).click();

      // Should show validation error
      await expect(
        page.getByText('Points must be a positive number')
      ).toBeVisible();
    }
  });
});

test.describe('ACT-3: Activity Icon Assignment', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/admin');
    await page.getByRole('tab', { name: 'Activities' }).click();
  });

  test.afterEach(async ({ page }) => {
    await cleanupTestActivityTypes(page);
  });

  test('should allow setting custom icons for activities', async ({ page }) => {
    await page.getByRole('button', { name: 'Create Activity' }).click();

    await page.getByLabel('Name').fill('Yoga Class');
    await page.getByLabel('Points').fill('15');
    await page.getByLabel('Icon Name').fill('yoga');

    await page.getByRole('button', { name: 'Create' }).click();

    await expect(
      page.getByText('Activity type successfully created!')
    ).toBeVisible();

    // Verify icon appears in table (this would depend on how icons are displayed)
    await expect(page.getByRole('cell', { name: 'Yoga Class' })).toBeVisible();
  });

  test('should validate icon name is required', async ({ page }) => {
    await page.getByRole('button', { name: 'Create Activity' }).click();

    await page.getByLabel('Name').fill('Test Activity');
    await page.getByLabel('Points').fill('10');
    // Leave icon name empty

    await page.getByRole('button', { name: 'Create' }).click();

    await expect(page.getByText('Icon name cannot be empty')).toBeVisible();
  });
});

test.describe('ACT-4: End User Activity Selection Interface', () => {
  test.beforeEach(async ({ page }) => {
    // First set up some activity types as admin
    await seedActivityData(page);

    // Then switch to member view
    await page.goto('/dashboard');
  });

  test.afterEach(async ({ page }) => {
    await cleanupTestActivityTypes(page);
  });

  test('should display log activity button on dashboard', async ({ page }) => {
    await expect(
      page.getByRole('button', { name: 'Log Activity' })
    ).toBeVisible();
  });

  test('should show dropdown with available activities when clicked', async ({
    page,
  }) => {
    await page.getByRole('button', { name: 'Log Activity' }).click();

    // Verify dropdown appears
    await expect(page.getByRole('menu')).toBeVisible();

    // Should show available activity types
    await expect(
      page.getByRole('menuitem', { name: /Class Attendance/ })
    ).toBeVisible();
  });

  test('should log activity and show success feedback', async ({ page }) => {
    await page.getByRole('button', { name: 'Log Activity' }).click();

    // Click on an activity type
    await page.getByRole('menuitem', { name: /Class Attendance/ }).click();

    // Verify success toast appears
    await expect(page.getByText(/Activity logged!/)).toBeVisible();
    await expect(page.getByText(/\+\d+ points/)).toBeVisible();

    // Verify dropdown closes
    await expect(page.getByRole('menu')).not.toBeVisible();
  });

  test('should show loading state while logging activity', async ({ page }) => {
    await page.getByRole('button', { name: 'Log Activity' }).click();

    const activityButton = page.getByRole('menuitem', {
      name: /Class Attendance/,
    });
    await activityButton.click();

    // Should show loading state briefly - reduced timeout for speed
    await expect(page.getByText('Logging activity...')).toBeVisible({
      timeout: 2000,
    });
  });

  test('should update points display after logging activity', async ({
    page,
  }) => {
    // Get initial points value
    const initialPointsText = await page
      .getByTestId('user-points')
      .textContent();
    const initialPoints = parseInt(initialPointsText?.match(/\d+/)?.[0] || '0');

    await page.getByRole('button', { name: 'Log Activity' }).click();
    await page.getByRole('menuitem', { name: /Class Attendance/ }).click();

    // Wait for success message
    await expect(page.getByText(/Activity logged!/)).toBeVisible();

    // Verify points increased
    await expect(page.getByTestId('user-points')).toContainText(
      (initialPoints + 10).toString()
    );
  });

  test('should handle error when activity logging fails', async ({ page }) => {
    // This test would require mocking a failure scenario
    // For now, we'll test the error handling structure exists
    await page.getByRole('button', { name: 'Log Activity' }).click();

    // The error handling should be in place even if we can't easily trigger it
    await expect(
      page.getByRole('menuitem', { name: /Class Attendance/ })
    ).toBeVisible();
  });

  // Visual snapshot test for the activity logging interface
  test('should match visual snapshot of activity logging dropdown', async ({
    page,
  }) => {
    await page.getByRole('button', { name: 'Log Activity' }).click();

    // Wait for dropdown to fully appear - reduced from 500ms to 200ms
    await page.waitForTimeout(200);

    await expect(page.getByRole('menu')).toHaveScreenshot(
      'activity-logging-dropdown.png'
    );
  });
});

test.describe('ACT-5: Generic Backend Activity Logging', () => {
  test.beforeEach(async ({ page }) => {
    await seedActivityData(page);
    await page.goto('/dashboard');
  });

  test.afterEach(async ({ page }) => {
    await cleanupTestActivityTypes(page);
  });

  test('should log different activity types with correct point values', async ({
    page,
  }) => {
    // Test logging different activity types and verify correct points are awarded
    const activityTests = [
      { name: 'Class Attendance', expectedPoints: 10 },
      { name: 'Personal Training', expectedPoints: 50 },
    ];

    for (const activity of activityTests) {
      const initialPointsText = await page
        .getByTestId('user-points')
        .textContent();
      const initialPoints = parseInt(
        initialPointsText?.match(/\d+/)?.[0] || '0'
      );

      await page.getByRole('button', { name: 'Log Activity' }).click();
      await page
        .getByRole('menuitem', { name: new RegExp(activity.name) })
        .click();

      // Verify correct points awarded
      await expect(
        page.getByText(`+${activity.expectedPoints} points`)
      ).toBeVisible();

      // Verify total points updated correctly
      await expect(page.getByTestId('user-points')).toContainText(
        (initialPoints + activity.expectedPoints).toString()
      );
    }
  });

  test('should record activity in user history', async ({ page }) => {
    await page.getByRole('button', { name: 'Log Activity' }).click();
    await page.getByRole('menuitem', { name: /Class Attendance/ }).click();

    await expect(page.getByText(/Activity logged!/)).toBeVisible();

    // Navigate to activity history
    await page.getByText('View all').click(); // Assuming this links to history

    // Verify activity appears in history
    await expect(page.getByText('Class Attendance')).toBeVisible();
    await expect(page.getByText('+10 Points')).toBeVisible();
  });
});

// Helper functions for test setup and cleanup
async function createTestActivity(
  page: any,
  name: string,
  points: number,
  icon: string
) {
  await page.getByRole('button', { name: 'Create Activity' }).click();
  await page.getByLabel('Name').fill(name);
  await page.getByLabel('Points').fill(points.toString());
  await page.getByLabel('Icon Name').fill(icon);
  await page.getByRole('button', { name: 'Create' }).click();
  await expect(
    page.getByText('Activity type successfully created!')
  ).toBeVisible();
}

async function seedActivityData(page: any) {
  // Navigate to admin panel and create test activities
  await page.goto('/admin');
  await page.getByRole('tab', { name: 'Activities' }).click();

  // Create a few test activities if they don't exist
  const activities = [
    { name: 'Personal Training', points: 50, icon: 'dumbbell' },
    { name: 'Group Fitness', points: 20, icon: 'users' },
  ];

  for (const activity of activities) {
    // Check if activity already exists
    const exists = await page
      .getByRole('cell', { name: activity.name })
      .isVisible()
      .catch(() => false);
    if (!exists) {
      await createTestActivity(
        page,
        activity.name,
        activity.points,
        activity.icon
      );
    }
  }
}

async function cleanupTestActivityTypes(page: any) {
  // This function would clean up any test activities created during tests
  // Implementation would depend on having a way to identify test activities
  // For now, this is a placeholder for the cleanup logic
  console.log('Cleaning up test activity types...');
}

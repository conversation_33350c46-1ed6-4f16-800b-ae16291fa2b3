import { chromium, FullConfig, expect } from '@playwright/test';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const STORAGE_STATE = path.join(__dirname, 'playwright/.auth/user.json');

async function globalSetup(config: FullConfig) {
  const { baseURL } = config.projects[0].use;
  const browser = await chromium.launch();
  const page = await browser.newPage();

  const email = process.env.E2E_USER_EMAIL;
  const password = process.env.E2E_USER_PASSWORD;

  if (!email || !password) {
    throw new Error(
      'E2E_USER_EMAIL and E2E_USER_PASSWORD environment variables must be set. Please add them to a .env file in the root of the project.'
    );
  }

  await page.goto(baseURL!);

  // Click the sign-in link to go to <PERSON>'s hosted page
  await page.getByRole('link', { name: 'Sign In' }).click();

  // Wait for the sign-in form to appear and enter email
  await page.waitForSelector('input[name="identifier"]');
  const emailInput = page.locator('input[name="identifier"]');
  await emailInput.waitFor({ state: 'visible', timeout: 10000 });
  await emailInput.fill(email);
  await page.getByRole('button', { name: 'Continue', exact: true }).click();

  // Enter password
  const passwordInput = page.locator('input[name="password"]');
  await passwordInput.waitFor({ state: 'visible', timeout: 10000 });
  await passwordInput.fill(password);
  await page
    .locator('button[data-localization-key="formButtonPrimary"]')
    .click();

  // Wait for the redirect back to the app and for the dashboard to be visible.
  // The dashboard URL could have a redirect query param, so we use a glob pattern.
  await page.waitForURL(`${baseURL}**/dashboard**`);
  await expect(page.getByRole('heading', { name: /Welcome/ })).toBeVisible({
    timeout: 10000,
  });

  // Save the storage state to the file
  await page.context().storageState({ path: STORAGE_STATE });
  await browser.close();
}

export default globalSetup;

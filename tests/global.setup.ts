import { chromium, FullConfig, expect } from '@playwright/test';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const STORAGE_STATE = path.join(__dirname, 'playwright/.auth/user.json');

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global authentication setup...');

  const { baseURL } = config.projects[0].use;
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Get credentials from environment or use defaults for development
    const email = process.env.E2E_USER_EMAIL || '<EMAIL>';
    const password = process.env.E2E_USER_PASSWORD;

    console.log(`📧 Using email: ${email}`);

    await page.goto(baseURL!);
    console.log('📱 Navigated to app');

    // Check if we're already authenticated by trying dashboard
    await page.goto(`${baseURL}/dashboard`);
    await page.waitForTimeout(1000);

    if (!page.url().includes('sign-in')) {
      console.log('✅ Already authenticated, saving current state');
      await page.context().storageState({ path: STORAGE_STATE });
      await browser.close();
      return;
    }

    console.log('🔐 Authentication required, starting sign-in process...');

    // Go to sign-in page
    await page.goto(`${baseURL}/sign-in`);

    // Wait for Clerk sign-in component
    await page.waitForSelector('.cl-rootBox', { timeout: 10000 });
    console.log('✅ Clerk component loaded');

    // Enter email
    const emailInput = page.locator(
      'input[name="identifier"], input[name="emailAddress"]'
    );
    await emailInput.waitFor({ state: 'visible', timeout: 10000 });
    await emailInput.fill(email);
    console.log('📧 Email entered');

    // Click continue
    await page.getByRole('button', { name: 'Continue', exact: true }).click();
    console.log('🔄 Clicked continue');

    // Handle password if required
    if (password) {
      const passwordInput = page.locator('input[name="password"]');
      await passwordInput.waitFor({ state: 'visible', timeout: 10000 });
      await passwordInput.fill(password);
      console.log('🔑 Password entered');

      await page
        .locator('button[data-localization-key="formButtonPrimary"]')
        .click();
      console.log('🔄 Clicked sign in');
    } else {
      console.log(
        '⚠️ No password provided - check if passwordless auth is configured'
      );
    }

    // Wait for successful authentication
    await page.waitForURL(`${baseURL}**/dashboard**`, { timeout: 15000 });
    console.log('🎉 Successfully authenticated!');

    // Verify we can see dashboard content
    await expect(
      page.getByRole('heading', { name: /Welcome|Dashboard/ })
    ).toBeVisible({
      timeout: 10000,
    });

    // Save authentication state
    await page.context().storageState({ path: STORAGE_STATE });
    console.log('💾 Authentication state saved');
  } catch (error) {
    console.error('❌ Authentication failed:', error);

    // Save whatever state we have for fallback
    await page.context().storageState({ path: STORAGE_STATE });
    console.log('💾 Saved fallback state');

    // Don't throw - let tests run with whatever auth state we have
    console.log(
      '⚠️ Continuing with limited authentication - some tests may be skipped'
    );
  } finally {
    await browser.close();
    console.log('🏁 Global setup complete');
  }
}

export default globalSetup;

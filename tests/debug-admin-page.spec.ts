import { test, expect } from '@playwright/test';
import { loginAs, requireAdminAccess } from './utils/auth';

test.describe('Debug Admin Page', () => {
  test('should show what the admin page looks like', async ({ page }) => {
    // Listen for console errors
    const consoleErrors: string[] = [];
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Listen for page errors
    const pageErrors: string[] = [];
    page.on('pageerror', (error) => {
      pageErrors.push(error.message);
    });

    // Authenticate
    await loginAs(page, { email: '<EMAIL>', role: 'admin' });
    await requireAdminAccess(page);

    // Navigate to admin page
    await page.goto('/admin');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(2000);

    // Log any errors
    if (consoleErrors.length > 0) {
      console.log('Console errors:', consoleErrors);
    }
    if (pageErrors.length > 0) {
      console.log('Page errors:', pageErrors);
    }

    // Take a screenshot
    await page.screenshot({ path: 'debug-admin-page.png', fullPage: true });

    // Log the page content
    const bodyText = await page.locator('body').textContent();
    console.log('Admin page content:', bodyText?.substring(0, 1000));

    // Check for common elements
    const buttons = await page.getByRole('button').all();
    console.log('Found buttons:');
    for (const button of buttons) {
      const text = await button.textContent();
      console.log(`  - "${text}"`);
    }

    // Check for headings
    const headings = await page.locator('h1, h2, h3').all();
    console.log('Found headings:');
    for (const heading of headings) {
      const text = await heading.textContent();
      console.log(`  - "${text}"`);
    }

    // Try clicking Activities button
    const activitiesButton = page
      .getByRole('button')
      .filter({ hasText: 'Activities' });
    if (await activitiesButton.isVisible()) {
      console.log('Activities button found, clicking...');
      await activitiesButton.click();
      await page.waitForTimeout(2000);

      // Take another screenshot after clicking
      await page.screenshot({
        path: 'debug-admin-after-activities-click.png',
        fullPage: true,
      });

      // Check what content is now visible
      const newBodyText = await page.locator('body').textContent();
      console.log(
        'After clicking Activities:',
        newBodyText?.substring(0, 1000)
      );
    } else {
      console.log('Activities button not found');
    }
  });
});

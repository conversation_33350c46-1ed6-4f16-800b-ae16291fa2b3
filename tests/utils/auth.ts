import { Page } from '@playwright/test';

/**
 * Helper to check if user is authenticated.
 * This is useful for verifying that the global setup was successful.
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    // Wait for an element that only appears when authenticated.
    await page.waitForSelector('[data-testid="authenticated-content"]', {
      timeout: 3000,
    });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Since authentication is handled by global setup, this function is a placeholder.
 * Tests should start with an authenticated user session.
 * @deprecated Authentication is now handled globally.
 */
export async function loginAs(page: Page, options: any): Promise<void> {
  // This function is intentionally left empty.
  // The global setup handles authentication.
  console.warn(
    'loginAs is deprecated and should be removed. Authentication is handled globally.'
  );
}

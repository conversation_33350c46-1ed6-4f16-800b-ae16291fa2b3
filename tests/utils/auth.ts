import { Page, expect } from '@playwright/test';

interface LoginOptions {
  email: string;
  role: 'admin' | 'staff' | 'member';
}

/**
 * Authentication helper for E2E tests
 * Since global setup handles authentication, this function mainly validates auth state
 */
export async function loginAs(
  page: Page,
  options: LoginOptions
): Promise<void> {
  const { email, role } = options;

  console.log(`🔐 Validating authentication for ${email} with role ${role}`);

  // Navigate to dashboard to verify authentication
  await page.goto('/dashboard');
  await page.waitForTimeout(500);

  // Check if we're authenticated
  if (page.url().includes('sign-in')) {
    console.warn('⚠️ Not authenticated - global setup may have failed');
    throw new Error(
      `Authentication required for test. Please ensure user ${email} exists in Clerk and E2E_USER_PASSWORD is set.`
    );
  }

  console.log('✅ Authentication validated successfully');
}

/**
 * Check if user has admin/staff access by trying to access admin page
 */
export async function requireAdminAccess(page: Page): Promise<void> {
  await page.goto('/admin');
  await page.waitForTimeout(500);

  if (page.url().includes('sign-in') || page.url().includes('dashboard')) {
    throw new Error(
      'Admin access required for this test. User may not have admin/staff role.'
    );
  }

  console.log('✅ Admin access confirmed');
}

/**
 * Navigate to admin activities tab (DRY helper for activity tests)
 */
export async function navigateToActivitiesTab(page: Page): Promise<void> {
  await page.goto('/admin');
  await page.waitForLoadState('domcontentloaded');

  // Wait for and click the Activities button (it's a button, not a tab)
  const activitiesButton = page
    .getByRole('button')
    .filter({ hasText: 'Activities' });
  await activitiesButton.waitFor({ state: 'visible', timeout: 5000 });
  await activitiesButton.click();

  // Wait for the activities management interface to load
  await expect(page.getByText('Manage Activities')).toBeVisible({
    timeout: 5000,
  });

  console.log('✅ Navigated to Activities management tab');
}

/**
 * Helper to logout the current user
 */
export async function logout(page: Page): Promise<void> {
  await page.evaluate(() => {
    localStorage.clear();
    sessionStorage.clear();
  });

  await page.goto('/');
  await page.waitForTimeout(500);
}

/**
 * Helper to check if user is authenticated
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    // Check for common authenticated elements
    const authElements = [
      '[data-testid="authenticated-content"]',
      '[data-testid="user-menu"]',
      '[data-testid="dashboard"]',
      'text=Dashboard',
      'text=Admin',
      'text=Log Activity',
    ];

    for (const selector of authElements) {
      try {
        await page.waitForSelector(selector, { timeout: 1000 });
        return true;
      } catch {
        continue;
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

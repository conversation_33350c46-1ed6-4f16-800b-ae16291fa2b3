import { Page } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';

interface LoginOptions {
  email: string;
  role: 'admin' | 'staff' | 'member';
}

/**
 * Custom authentication helper for E2E tests
 * Uses existing Clerk authentication or attempts manual login
 */
export async function loginAs(
  page: Page,
  options: LoginOptions
): Promise<void> {
  const { email, role } = options;

  // Try to load existing Clerk authentication
  const authFilePath = path.join(__dirname, '../playwright/.auth/user.json');

  try {
    if (fs.existsSync(authFilePath)) {
      const authData = JSON.parse(fs.readFileSync(authFilePath, 'utf8'));
      await page.context().addCookies(authData.cookies);
      console.log(
        '✅ Loaded Clerk authentication cookies for:',
        email,
        'role:',
        role
      );
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.warn('⚠️ Could not load auth cookies:', errorMessage);
  }

  // Navigate to dashboard to test authentication
  await page.goto('/dashboard');
  await page.waitForTimeout(1000);

  // Check if we're redirected to sign-in (meaning auth failed)
  if (page.url().includes('sign-in')) {
    console.log('🔐 Authentication required, attempting sign-in...');

    // For now, we'll skip the actual sign-in process
    // In a real test environment, you would:
    // 1. Set up test users in Clerk
    // 2. Use Clerk's test mode
    // 3. Implement the full sign-in flow

    console.warn(
      '⚠️ Authentication not implemented - tests will run unauthenticated'
    );
    return;
  }

  console.log('✅ Authentication successful');
}

/**
 * Helper to logout the current user
 */
export async function logout(page: Page): Promise<void> {
  await page.evaluate(() => {
    localStorage.clear();
    sessionStorage.clear();
  });

  await page.goto('/');
  await page.waitForTimeout(500);
}

/**
 * Helper to check if user is authenticated
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    // Check for common authenticated elements
    const authElements = [
      '[data-testid="authenticated-content"]',
      '[data-testid="user-menu"]',
      '[data-testid="dashboard"]',
      'text=Dashboard',
      'text=Admin',
      'text=Log Activity',
    ];

    for (const selector of authElements) {
      try {
        await page.waitForSelector(selector, { timeout: 1000 });
        return true;
      } catch {
        continue;
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

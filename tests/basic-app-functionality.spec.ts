import { test, expect } from '@playwright/test';

test.describe('Basic App Functionality', () => {
  test('should load the landing page correctly (authenticated)', async ({
    page,
  }) => {
    await page.goto('/');

    // Check that the page loads
    await expect(page).toHaveTitle('Fitness Rewards Platform');

    // Check for main heading
    await expect(page.getByText('Welcome to FitRewards')).toBeVisible();

    // Since we're authenticated via global setup, check for authenticated navigation
    await expect(
      page.getByRole('link', { name: 'Dashboard', exact: true })
    ).toBeVisible();
    await expect(page.getByRole('link', { name: 'FitRewards' })).toBeVisible();

    // Check for main CTA
    await expect(page.getByText('Go to Dashboard')).toBeVisible();
  });

  test('should navigate to sign-in page (unauthenticated)', async ({
    page,
  }) => {
    // Clear authentication state for this test
    await page.context().clearCookies();
    await page.goto('/');

    // Click sign-in link
    await page.getByText('Sign In').click();

    // Should navigate to sign-in page
    await expect(page).toHaveURL(/.*sign-in/);

    // Should show Clerk sign-in component
    await expect(page.locator('.cl-rootBox')).toBeVisible({ timeout: 10000 });
  });

  test('should navigate to sign-up page (unauthenticated)', async ({
    page,
  }) => {
    // Clear authentication state for this test
    await page.context().clearCookies();
    await page.goto('/');

    // Click sign-up link
    await page.getByText('Sign Up').click();

    // Should navigate to sign-up page
    await expect(page).toHaveURL(/.*sign-up/);

    // Should show Clerk sign-up component
    await expect(page.locator('.cl-rootBox')).toBeVisible({ timeout: 10000 });
  });

  test('should redirect to sign-in when accessing protected routes (unauthenticated)', async ({
    page,
  }) => {
    // Clear authentication state for this test
    await page.context().clearCookies();

    // Try to access dashboard without authentication
    await page.goto('/dashboard');

    // Should be redirected to sign-in
    await expect(page).toHaveURL(/.*sign-in/);
  });

  test('should redirect to sign-in when accessing admin routes (unauthenticated)', async ({
    page,
  }) => {
    // Clear authentication state for this test
    await page.context().clearCookies();

    // Try to access admin without authentication
    await page.goto('/admin');

    // Should be redirected to sign-in
    await expect(page).toHaveURL(/.*sign-in/);
  });

  test('should have proper navigation structure', async ({ page }) => {
    await page.goto('/');

    // Check navigation elements
    await expect(page.getByRole('navigation')).toBeVisible();
    await expect(page.getByRole('link', { name: 'FitRewards' })).toBeVisible(); // Logo/brand link

    // Check footer
    await expect(
      page.getByText('© 2025 Fitness Rewards Platform')
    ).toBeVisible();
  });

  test('should handle 404 routes gracefully', async ({ page }) => {
    await page.goto('/non-existent-route');

    // Should show some kind of error or redirect
    // The exact behavior depends on your 404 handling
    const title = await page.title();
    expect(title).toBe('Fitness Rewards Platform');
  });

  test('should have responsive design elements', async ({ page }) => {
    await page.goto('/');

    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Main elements should still be visible
    await expect(page.getByText('Welcome to FitRewards')).toBeVisible();
    await expect(page.getByText('Sign In')).toBeVisible();

    // Test desktop viewport
    await page.setViewportSize({ width: 1200, height: 800 });

    // Elements should still be visible
    await expect(page.getByText('Welcome to FitRewards')).toBeVisible();
    await expect(page.getByText('Sign In')).toBeVisible();
  });

  test('should load without JavaScript errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    const pageErrors: string[] = [];
    page.on('pageerror', (error) => {
      pageErrors.push(error.message);
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Check for critical errors (allow some warnings)
    const criticalErrors = consoleErrors.filter(
      (error) =>
        !error.includes('Warning') &&
        !error.includes('DevTools') &&
        !error.includes('Extension')
    );

    if (criticalErrors.length > 0) {
      console.log('Console errors found:', criticalErrors);
    }

    if (pageErrors.length > 0) {
      console.log('Page errors found:', pageErrors);
    }

    // For now, just log errors instead of failing
    // In a production environment, you'd want to fail on critical errors
    expect(pageErrors.length).toBeLessThan(5); // Allow some minor errors
  });

  test('should have proper meta tags and SEO elements', async ({ page }) => {
    await page.goto('/');

    // Check title
    await expect(page).toHaveTitle('Fitness Rewards Platform');

    // Check that React app is rendering
    const reactRoot = await page.locator('#root').isVisible();
    expect(reactRoot).toBe(true);
  });
});

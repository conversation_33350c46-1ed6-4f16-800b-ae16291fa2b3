{"name": "fitness-rewards-platform-monorepo", "private": true, "type": "module", "packageManager": "pnpm@8.15.0", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev --parallel", "dev:db": "turbo dev --filter=@fitness-rewards/db", "dev:web": "turbo dev --filter=@fitness-rewards/web", "build": "turbo build", "test": "turbo test", "test:e2e": "playwright test", "test:e2e:fast": "playwright test --project=chromium --workers=4", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "lint": "turbo lint", "type-check": "turbo type-check", "clean": "turbo clean && rm -rf node_modules", "reinstall": "./scripts/reinstall.sh", "format": "prettier --write \"**/*.{ts,tsx,md,json,css,scss}\""}, "devDependencies": {"@playwright/test": "^1.53.0", "@types/node": "^20.12.8", "dotenv": "^16.5.0", "eslint": "^8.57.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.13", "turbo": "^1.13.3", "typescript": "^5.4.5"}}
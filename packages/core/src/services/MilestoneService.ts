/**
 * MilestoneService - Core business logic for milestone evaluation
 *
 * This service handles the evaluation of user activities against milestone criteria
 * and determines when milestones are achieved.
 */

// NOTE: The 'core' package should not depend on database-specific types like `Doc`.
// We define a pure `Milestone` type here to keep the business logic decoupled.
export interface Milestone {
  _id: string;
  name: string;
  isEnabled: boolean;
  isRepeatable: boolean;
  conditions: {
    activityTypeMatcher: string;
    countThreshold: number;
  };
  rewards: {
    type: 'points' | 'badge';
    value: number | string;
  }[];
}

export interface MilestoneEvaluationInput {
  // A map of all activity type keys to their total counts for the user
  activityCounts: Record<string, number>;
  availableMilestones: Milestone[];
  // An array of milestone IDs the user has already achieved
  achievedMilestoneIds: Set<string>;
}

export interface MilestoneEvaluationResult {
  newlyAchieved: Milestone[];
  pointsAwarded: number;
}

/**
 * Evaluates if a user has achieved any new milestones based on their activities
 *
 * @param input Evaluation input containing user activities and available milestones
 * @returns Result containing newly achieved milestones and rewards
 */
export function evaluateUserMilestones(
  input: MilestoneEvaluationInput
): MilestoneEvaluationResult {
  const { activityCounts, availableMilestones, achievedMilestoneIds } = input;

  const result: MilestoneEvaluationResult = {
    newlyAchieved: [],
    pointsAwarded: 0,
  };

  // Evaluate each eligible milestone
  for (const milestone of availableMilestones) {
    if (!milestone.isEnabled) {
      continue;
    }

    // Skip already achieved non-repeatable milestones
    if (!milestone.isRepeatable && achievedMilestoneIds.has(milestone._id)) {
      continue;
    }

    const { activityTypeMatcher, countThreshold } = milestone.conditions;
    const activityCount = activityCounts[activityTypeMatcher] || 0;

    // Check if milestone conditions are met
    // For repeatable milestones, this logic may need to be more advanced in the future
    // (e.g., checking for count modulo threshold), but for now, we check if the threshold is met.
    if (activityCount >= countThreshold) {
      // This simple check works for first-time and repeatable achievements where progress isn't reset.
      // A more complex system would be needed if repeatable milestones reset progress.
      if (!achievedMilestoneIds.has(milestone._id) || milestone.isRepeatable) {
        // To prevent awarding repeatable milestones on every single activity log after achieving it,
        // we'll assume for now that the calling function handles the "newly achieved" logic.
        // A better implementation for *true* repeatable milestones would track progress since last achievement.
        // For this PRD, we are focused on the initial achievement.
        result.newlyAchieved.push(milestone);
        const pointsReward = milestone.rewards.find((r) => r.type === 'points');
        const points = pointsReward ? (pointsReward.value as number) : 0;
        result.pointsAwarded += points;
      }
    }
  }

  // To avoid awarding points for the same non-repeatable milestone multiple times,
  // we filter out any that have already been achieved.
  const finalResult: MilestoneEvaluationResult = {
    newlyAchieved: [],
    pointsAwarded: 0,
  };

  const awardedRepeatable = new Set<string>();

  for (const milestone of result.newlyAchieved) {
    if (!achievedMilestoneIds.has(milestone._id)) {
      finalResult.newlyAchieved.push(milestone);
      const points =
        (milestone.rewards.find((r) => r.type === 'points')?.value as number) ||
        0;
      finalResult.pointsAwarded += points;
    } else if (
      milestone.isRepeatable &&
      !awardedRepeatable.has(milestone._id)
    ) {
      // This part is tricky. A simple "logActivity" might not be enough context for repeatable milestones.
      // For now, let's assume repeatable milestones are not the focus and we avoid re-awarding points for them
      // unless specifically handled by the caller. The current logic focuses on new achievements.
    }
  }

  return finalResult;
}

export const MilestoneService = {
  evaluateUserMilestones,
};

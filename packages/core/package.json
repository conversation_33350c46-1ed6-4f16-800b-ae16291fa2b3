{"name": "@fitness-rewards/core", "private": true, "version": "0.1.0", "type": "module", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./services/TierService": "./src/services/TierService.ts", "./services/MilestoneService": "./src/services/MilestoneService.ts"}, "scripts": {"build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/jest": "^29.5.12", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.4.5"}}
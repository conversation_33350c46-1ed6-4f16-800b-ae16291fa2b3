import { Resend } from 'resend';
import { render } from '@react-email/render';
import { MemberRedemptionEmail } from '../templates/MemberRedemptionEmail';
import { StaffNotificationEmail } from '../templates/StaffNotificationEmail';
import React from 'react';

// We use lazy initialization for the Resend client to prevent server startup
// errors when the API key environment variable is not set. The client is
// instantiated only when the first email is sent.
let resend: Resend | undefined;

/**
 * <PERSON>zily initializes and returns a singleton Resend client instance.
 * This prevents app crashes during startup if the API key is not configured.
 * @returns {Resend} The Resend client instance.
 * @throws {Error} If the RESEND_API_KEY environment variable is not set.
 */
const getResendClient = (): Resend => {
  if (!resend) {
    const apiKey = process.env.RESEND_API_KEY;
    if (!apiKey) {
      // For local development, add `RESEND_API_KEY="re_..."` to your .env.local
      // file. A valid key format is required, but it can be a dummy value if
      // you do not intend to send emails.
      console.warn(
        'RESEND_API_KEY environment variable not set. Using dummy key. Email sending will fail.'
      );
      resend = new Resend('re_dummy_key_for_local_dev');
    } else {
      resend = new Resend(apiKey);
    }
  }
  return resend;
};

/**
 * @param to Recipient's email address
 * @param subject Email subject
 * @param body React element to render as email body
 */
async function sendEmailInternal(
  from: string,
  to: string,
  subject: string,
  body: React.ReactElement
) {
  const html = await render(body);
  const { data, error } = await getResendClient().emails.send({
    from,
    to,
    subject,
    html,
  });

  if (error) {
    // TODO: Add structured logging
    console.error(
      `Failed to send email to ${to} with subject "${subject}"`,
      error
    );
    throw new Error('Failed to send email');
  }

  return data;
}

/**
 * Generic email sending function
 */
export async function sendEmail({
  to,
  subject,
  html,
  from,
}: {
  to: string;
  subject: string;
  html: string;
  from: string;
}) {
  const { data, error } = await getResendClient().emails.send({
    from,
    to,
    subject,
    html,
  });

  if (error) {
    console.error(
      `Failed to send email to ${to} with subject "${subject}"`,
      error
    );
    throw new Error('Failed to send email');
  }

  return data;
}

type MemberRedemptionEmailProps = React.ComponentProps<
  typeof MemberRedemptionEmail
>;

export async function sendMemberRedemptionEmail(
  from: string,
  to: string,
  props: MemberRedemptionEmailProps
) {
  const subject = `Your Reward Redemption: ${props.rewardName}!`;
  return sendEmailInternal(
    from,
    to,
    subject,
    React.createElement(MemberRedemptionEmail, props)
  );
}

type StaffNotificationEmailProps = React.ComponentProps<
  typeof StaffNotificationEmail
>;

export async function sendStaffNotificationEmail(
  from: string,
  to: string,
  props: StaffNotificationEmailProps
) {
  const subject = `New Reward Redemption: ${props.rewardName}`;
  return sendEmailInternal(
    from,
    to,
    subject,
    React.createElement(StaffNotificationEmail, props)
  );
}

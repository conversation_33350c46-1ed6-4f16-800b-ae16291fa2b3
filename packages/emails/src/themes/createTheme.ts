/**
 * @file Contains the logic for creating a complete email theme.
 */

import { defaultTheme } from './defaultTheme';
import { type EmailBranding, type EmailTheme } from './types';

/**
 * A simple utility to determine if a color is light or dark.
 * @param color - A hex color string.
 * @returns `true` if the color is light, `false` if dark.
 */
function isColorLight(color: string): boolean {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 155;
}

/**
 * Merges a partial client branding configuration with the default theme
 * to create a complete, ready-to-use email theme.
 *
 * It also calculates text colors for optimal contrast against the primary color.
 *
 * @param branding - The client's branding configuration.
 * @returns A complete `EmailTheme` object.
 */
export function createTheme(branding: Partial<EmailBranding> = {}): EmailTheme {
  const primaryColor = branding.primaryColor || defaultTheme.colors.primary;
  const buttonTextColor = isColorLight(primaryColor) ? '#000000' : '#FFFFFF';

  return {
    ...defaultTheme,
    logoUrl: branding.logoUrl || defaultTheme.logoUrl,
    colors: {
      ...defaultTheme.colors,
      primary: primaryColor,
      secondary: branding.secondaryColor || defaultTheme.colors.secondary,
      accent: branding.accentColor || defaultTheme.colors.accent,
      background: branding.backgroundColor || defaultTheme.colors.background,
    },
    button: {
      backgroundColor: primaryColor,
      color: buttonTextColor,
    },
    footerText: branding.footerText || defaultTheme.footerText,
  };
}

/**
 * @file Defines the core types for email branding and theming.
 */

/**
 * Represents the raw branding configuration that a client can provide.
 * These are the values stored in the `clientConfiguration.emailBranding` object.
 */
export interface EmailBranding {
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
  backgroundColor?: string;
  footerText?: string;
}

/**
 * Represents a complete, processed theme for an email.
 * This is the result of merging client-specific `EmailBranding` with the default theme.
 * It ensures that all properties required by email components are present.
 */
export interface EmailTheme {
  // Brand Identity
  logoUrl: string;
  // Color Palette
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string; // Calculated for contrast
    secondaryText: string; // Calculated for contrast
  };
  // Typography
  fontFamily: string;
  // Components
  button: {
    backgroundColor: string;
    color: string;
  };
  // Miscellaneous
  footerText: string;
}

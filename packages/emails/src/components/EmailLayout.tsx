/**
 * @file A base layout component for all emails to ensure consistency.
 */
import { Body, Container, Head, Html, Preview } from '@react-email/components';
import { BrandedHeader } from './BrandedHeader';
import { Footer } from './Footer';
import { type EmailTheme } from '../themes/types';

interface EmailLayoutProps {
  theme: EmailTheme;
  previewText: string;
  children: React.ReactNode;
}

/**
 * A wrapper for all emails that provides a consistent header, footer,
 * and body styling.
 * @param {EmailLayoutProps} props The props for the component.
 * @returns {React.ReactElement} The layout component.
 */
export const EmailLayout = ({
  theme,
  previewText,
  children,
}: EmailLayoutProps): React.ReactElement => {
  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body
        style={{
          backgroundColor: theme.colors.background,
          fontFamily: theme.fontFamily,
          color: theme.colors.text,
        }}
      >
        <Container
          style={{
            margin: '0 auto',
            padding: '20px 0 48px',
            width: '580px',
          }}
        >
          <BrandedHeader theme={theme} />
          {children}
          <Footer theme={theme} />
        </Container>
      </Body>
    </Html>
  );
};

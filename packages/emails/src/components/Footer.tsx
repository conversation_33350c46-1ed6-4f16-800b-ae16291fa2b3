/**
 * @file A themeable footer component for emails.
 */
import { Hr, Section, Text } from '@react-email/components';
import { marked } from 'marked';
import sanitizeHtml from 'sanitize-html';
import { type EmailTheme } from '../themes/types';

interface FooterProps {
  theme: EmailTheme;
}

/**
 * An email footer that displays the client's custom footer text.
 * It parses markdown and sanitizes the output for security.
 * @param {FooterProps} props The props for the component.
 * @returns {React.ReactElement} The themed footer component.
 */
export const Footer = ({ theme }: FooterProps): React.ReactElement => {
  const unsafeHtml = marked.parse(theme.footerText);
  // It is crucial to sanitize any user-provided content before rendering it as HTML.
  const safeHtml = sanitizeHtml(unsafeHtml as string);

  return (
    <Section>
      <Hr style={{ borderColor: theme.colors.secondary, margin: '20px 0' }} />
      <Text
        style={{
          color: theme.colors.secondaryText,
          fontSize: '12px',
          lineHeight: '24px',
        }}
        dangerouslySetInnerHTML={{ __html: safeHtml }}
      />
    </Section>
  );
};

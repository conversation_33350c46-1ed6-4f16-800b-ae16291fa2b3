import { z } from 'zod';

/**
 * Zod schema for creating a new activity type.
 * This is used for form validation in the admin UI and on the server.
 */
export const activityTypeCreateSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters long.'),
  key: z
    .string()
    .min(3, 'Key must be at least 3 characters long.')
    .regex(
      /^[a-z0-9_]+$/,
      'Key can only contain lowercase letters, numbers, and underscores.'
    ),
  points: z.coerce.number().int().positive('Points must be a positive number.'),
  iconName: z.string().min(1, 'Icon name cannot be empty.'),
});

export type ActivityTypeCreate = z.infer<typeof activityTypeCreateSchema>;

/**
 * Zod schema for updating an existing activity type.
 * Includes the ID of the activity type to be updated.
 */
export const activityTypeUpdateSchema = activityTypeCreateSchema.extend({
  id: z.string(),
});

export type ActivityTypeUpdate = z.infer<typeof activityTypeUpdateSchema>;

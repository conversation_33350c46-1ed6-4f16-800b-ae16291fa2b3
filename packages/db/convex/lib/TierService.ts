/**
 * TierService - Convex integration for tier progression system
 *
 * This service provides database-integrated tier calculation and progression tracking.
 * Imports core tier logic from packages/core and adds database persistence.
 */

import {
  calculateUserTier,
  getTierInfo,
  getNextTierInfo,
  TIER_THRESHOLDS,
  type TierCalculationResult,
} from '@fitness-rewards/core';
import type { MutationCtx, QueryCtx } from '../_generated/server';
import type { Id } from '../_generated/dataModel';

export interface TierEvaluationResult {
  hasAdvanced: boolean;
  previousTier: string;
  newTier: string;
  tierProgress: TierCalculationResult;
  tierAdvancementId?: Id<'tierProgressions'>;
}

/**
 * Evaluate user's tier based on current points and update if advanced
 *
 * @param ctx Convex mutation context
 * @param userId User ID
 * @param currentPoints User's current points
 * @param currentTier User's current tier
 * @param clientId Client ID for tracking
 * @returns Tier evaluation result with advancement status
 */
export async function evaluateAndUpdateUserTier(
  ctx: MutationCtx,
  userId: Id<'users'>,
  currentPoints: number,
  currentTier: string,
  clientId: Id<'clients'>
): Promise<TierEvaluationResult> {
  // Calculate new tier based on points
  const tierCalculation = calculateUserTier({
    currentPoints,
    currentTier,
  });

  const result: TierEvaluationResult = {
    hasAdvanced: tierCalculation.hasAdvanced,
    previousTier: currentTier,
    newTier: tierCalculation.newTier,
    tierProgress: tierCalculation,
  };

  // If user has advanced, update their tier and record the progression
  if (tierCalculation.hasAdvanced) {
    console.log(
      `🎉 User ${userId} advanced from ${currentTier} to ${tierCalculation.newTier}`
    );

    // Update user's tier in database
    await ctx.db.patch(userId, {
      tier: tierCalculation.newTier,
    });

    // Record tier progression history
    const tierAdvancementId = await ctx.db.insert('tierProgressions', {
      userId,
      previousTier: currentTier,
      newTier: tierCalculation.newTier,
      pointsAtAdvancement: currentPoints,
      advancedAt: Date.now(),
      clientId,
    });

    result.tierAdvancementId = tierAdvancementId;
  }

  return result;
}

/**
 * Get user's tier progression history
 *
 * @param ctx Convex query context
 * @param userId User ID
 * @returns Array of tier progressions ordered by advancement date
 */
export async function getUserTierHistory(ctx: QueryCtx, userId: Id<'users'>) {
  return await ctx.db
    .query('tierProgressions')
    .withIndex('by_user_id', (q) => q.eq('userId', userId))
    .order('desc')
    .collect();
}

/**
 * Get tier distribution statistics for community context
 *
 * @param ctx Convex query context
 * @param clientId Optional client ID to filter by specific client
 * @returns Tier distribution statistics
 */
export async function getTierDistribution(
  ctx: QueryCtx,
  clientId?: Id<'clients'>
) {
  let usersQuery = ctx.db.query('users');

  if (clientId) {
    // Note: This assumes we have an index by clientId, if not we'll need to collect and filter
    const users = await usersQuery.collect();
    const filteredUsers = users.filter((u) => u.clientId === clientId);

    const tierCounts: Record<string, number> = {};
    TIER_THRESHOLDS.forEach((tier) => {
      tierCounts[tier.tier] = 0;
    });

    filteredUsers.forEach((user) => {
      if (tierCounts[user.tier] !== undefined) {
        tierCounts[user.tier]++;
      }
    });

    return {
      totalUsers: filteredUsers.length,
      tierDistribution: tierCounts,
    };
  } else {
    const users = await usersQuery.collect();

    const tierCounts: Record<string, number> = {};
    TIER_THRESHOLDS.forEach((tier) => {
      tierCounts[tier.tier] = 0;
    });

    users.forEach((user) => {
      if (tierCounts[user.tier] !== undefined) {
        tierCounts[user.tier]++;
      }
    });

    return {
      totalUsers: users.length,
      tierDistribution: tierCounts,
    };
  }
}

/**
 * Calculate user's percentile within their client's tier distribution
 *
 * @param ctx Convex query context
 * @param userTier User's current tier
 * @param clientId Client ID for filtering
 * @returns User's percentile ranking
 */
export async function getUserTierPercentile(
  ctx: QueryCtx,
  userTier: string,
  clientId?: Id<'clients'>
): Promise<number> {
  const distribution = await getTierDistribution(ctx, clientId);

  const userTierIndex = TIER_THRESHOLDS.findIndex((t) => t.tier === userTier);
  let usersAtOrBelow = 0;

  for (let i = 0; i <= userTierIndex; i++) {
    usersAtOrBelow +=
      distribution.tierDistribution[TIER_THRESHOLDS[i].tier] || 0;
  }

  return distribution.totalUsers > 0
    ? Math.round((usersAtOrBelow / distribution.totalUsers) * 100)
    : 0;
}

// Re-export core tier utilities for use in Convex functions
export { calculateUserTier, getTierInfo, getNextTierInfo, TIER_THRESHOLDS };

import { Id, Doc } from '../_generated/dataModel';
import { DatabaseWriter } from '../_generated/server';

/**
 * MilestoneService - Core business logic for milestone evaluation
 *
 * This service handles the evaluation of user activities against milestone criteria
 * and determines when milestones are achieved.
 */

export interface Activity {
  _id: string;
  userId: string;
  clientId: string;
  activityTypeKey: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface Milestone {
  id: string;
  name: string;
  description: string;
  triggerType: string;
  conditions: {
    activityTypeMatcher: string;
    countThreshold: number;
  };
  rewards: Array<{
    type: string;
    value: string | number;
  }>;
  isRepeatable: boolean;
  isEnabled: boolean;
}

export interface MilestoneEvaluationInput {
  userId: string;
  activityTypeKey: string;
  userActivities: Activity[];
  availableMilestones: Milestone[];
  achievedMilestones: string[];
}

export interface MilestoneEvaluationResult {
  newlyAchieved: Milestone[];
  pointsAwarded: number;
  badgesEarned: string[];
}

/**
 * Evaluates if a user has achieved any new milestones based on their activities
 *
 * @param input Evaluation input containing user activities and available milestones
 * @returns Result containing newly achieved milestones and rewards
 */
export function evaluateUserMilestones(
  input: MilestoneEvaluationInput
): MilestoneEvaluationResult {
  const {
    activityTypeKey,
    userActivities,
    availableMilestones,
    achievedMilestones,
  } = input;

  // Initialize result
  const result: MilestoneEvaluationResult = {
    newlyAchieved: [],
    pointsAwarded: 0,
    badgesEarned: [],
  };

  // Filter enabled milestones that match the current activity type
  const eligibleMilestones = availableMilestones.filter(
    (milestone) =>
      milestone.isEnabled &&
      milestone.conditions.activityTypeMatcher === activityTypeKey &&
      // Skip already achieved non-repeatable milestones
      (milestone.isRepeatable || !achievedMilestones.includes(milestone.id))
  );

  // Count activities by type
  const activityCounts: Record<string, number> = {};
  userActivities.forEach((activity) => {
    activityCounts[activity.activityTypeKey] =
      (activityCounts[activity.activityTypeKey] || 0) + 1;
  });

  // Evaluate each eligible milestone
  eligibleMilestones.forEach((milestone) => {
    const { activityTypeMatcher, countThreshold } = milestone.conditions;
    const activityCount = activityCounts[activityTypeMatcher] || 0;

    // Check if milestone conditions are met
    if (activityCount >= countThreshold) {
      result.newlyAchieved.push(milestone);

      // Process rewards
      milestone.rewards.forEach((reward) => {
        if (reward.type === 'points') {
          result.pointsAwarded += Number(reward.value);
        } else if (reward.type === 'badge') {
          result.badgesEarned.push(reward.value as string);
        }
      });
    }
  });

  return result;
}

export default {
  evaluateUserMilestones,
};

/**
 * This is a placeholder for a more robust evaluation engine.
 */

export interface MilestoneEvaluationParams {
  userId: Id<'users'>;
  activityTypeKey: string;
  allUserActivities: Doc<'activities'>[];
}

/**
 * A simplified representation of a milestone for evaluation purposes.
 * In a real application, this would be backed by the `milestones` table.
 */
interface MilestoneRule {
  id: string;
  name: string;
  activityTypeKey: string;
  threshold: number;
  points: number;
}

/**
 * Awards a milestone to a user and records their achievement
 *
 * @param params - Object containing database instance, milestone rule, and user ID
 * @param params.db - Database writer instance
 * @param params.milestone - The milestone rule being awarded
 * @param params.userId - ID of the user receiving the milestone
 */
async function awardMilestone({
  db,
  milestone,
  userId,
}: {
  db: DatabaseWriter;
  milestone: MilestoneRule;
  userId: Id<'users'>;
}) {
  console.log(`Awarding milestone "${milestone.name}" to user ${userId}`);
  // Award points
  const user = await db.get(userId);
  if (user) {
    await db.patch(user._id, { points: user.points + milestone.points });
  }
  // Record achievement
  await db.insert('userMilestoneProgress', {
    userId,
    milestoneId: milestone.id,
    achievedAt: Date.now(),
    pointsEarned: milestone.points,
    // Denormalized fields for display/query convenience
    milestoneName: milestone.name,
    milestoneDescription: `Achieved by completing ${milestone.threshold} ${milestone.activityTypeKey} activities.`,
    milestoneIconUrl: 'default-icon-url', // Placeholder
    badgesEarned: [],
  });
}

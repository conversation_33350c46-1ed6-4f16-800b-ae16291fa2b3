/**
 * @file Convex actions for handling email sending.
 */
import { internal } from './_generated/api';
import { internalAction } from './_generated/server';
import { v } from 'convex/values';

export const send = internalAction({
  args: {
    to: v.string(),
    subject: v.string(),
    template: v.string(),
    props: v.any(),
    userId: v.id('users'),
  },
  handler: async (ctx, { to, subject, template, props, userId }) => {
    // 1. Look up the user and their associated client
    const user = await ctx.runQuery(internal.functions.users.internalGetById, {
      userId,
    });
    if (!user || !user.clientId) {
      console.error(`User ${userId} or their client could not be found.`);
      return;
    }

    // 2. Fetch the client's configuration
    const config = await ctx.runQuery(
      internal.functions.clientConfigs.getClientConfig,
      {
        clientId: user.clientId,
      }
    );

    if (!config) {
      console.error(`Configuration for client ${user.clientId} not found.`);
      return;
    }

    // 3. Create a basic theme (simplified for now)
    const theme = {
      colors: {
        primary: '#D4A574',
        secondary: '#F5F2E8',
        primaryText: '#2D2D2D',
        secondaryText: '#666666',
      },
      fonts: {
        heading: 'Arial, sans-serif',
        body: 'Arial, sans-serif',
      },
      footerText: 'Thank you for being part of our community!',
    };

    // 4. Dynamically import email functions to avoid Convex analysis issues
    const { sendEmail, MemberRedemptionEmail } = await import(
      '@fitness-rewards/emails'
    );
    const { render } = await import('@react-email/render');
    const React = await import('react');

    // A map of template names to their React components
    const templates = {
      memberRedemption: MemberRedemptionEmail,
      // staffNotification: StaffNotificationEmail,
    };

    // 5. Render the specified template
    const TemplateComponent = templates[template as keyof typeof templates];
    if (!TemplateComponent) {
      console.error(`Email template "${template}" not found.`);
      return;
    }

    // Use React.createElement to avoid JSX parsing issues in Convex environment
    const emailElement = React.createElement(TemplateComponent, {
      ...props,
      theme,
    });
    const emailHtml = render(emailElement);

    // 6. Dispatch the email
    await sendEmail({
      to,
      subject,
      html: emailHtml,
      from: config.fromEmail ?? '<EMAIL>',
    });
  },
});

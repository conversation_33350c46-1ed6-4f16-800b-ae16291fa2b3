import { ConvexError, v } from 'convex/values';
import {
  internalMutation,
  mutation,
  MutationCtx,
  query,
  QueryCtx,
} from '../_generated/server';
import { requireAuthenticatedUser } from '../lib/authHelpers';
import { evaluateAndUpdateUserTier } from '../lib/TierService';
import { Doc, Id } from '../_generated/dataModel';
import { MilestoneService } from '@fitness-rewards/core';

// =========== Core Internal Logic ===========

/**
 * The core logic for logging an activity, shared by internal and public mutations.
 * It handles point calculation, milestone evaluation, and tier updates.
 *
 * @param ctx - The mutation context.
 * @param user - The user for whom the activity is being logged.
 * @param activityType - The type of activity being logged.
 * @param source - The source of the activity log ('manual', 'webhook', 'api_poll').
 * @param externalReference - Optional reference for activities logged from external systems.
 * @returns An object with the results of the operation.
 */
const _logActivityCore = async (
  ctx: MutationCtx,
  user: Doc<'users'>,
  activityType: Doc<'activityTypes'>,
  source: 'manual' | 'webhook' | 'api_poll',
  externalReference?: {
    provider: string;
    externalId: string;
    externalTimestamp?: number;
  }
) => {
  const { db } = ctx;
  const { clientId, _id: userId } = user;
  const { key: activityTypeKey, points: activityPoints } = activityType;

  // 1. Record the raw activity
  await db.insert('activities', {
    userId,
    clientId: clientId!,
    activityTypeKey,
    timestamp: Date.now(),
    pointsAwarded: activityPoints,
    source,
    externalReference,
  });

  // 2. Update user's specific activity count
  const userActivityCount = await db
    .query('userActivityCounts')
    .withIndex('by_user_and_activity', (q) =>
      q.eq('userId', userId).eq('activityTypeKey', activityTypeKey)
    )
    .unique();

  const newCount = (userActivityCount?.count || 0) + 1;

  if (userActivityCount) {
    await db.patch(userActivityCount._id, { count: newCount });
  } else {
    await db.insert('userActivityCounts', {
      userId,
      activityTypeKey,
      count: newCount,
    });
  }

  // 3. Evaluate milestones using the centralized MilestoneService
  const allActivityCountsForUser = await db
    .query('userActivityCounts')
    .withIndex('by_user_and_activity', (q) => q.eq('userId', userId))
    .collect();

  const activityCountsMap = allActivityCountsForUser.reduce(
    (acc, curr) => {
      acc[curr.activityTypeKey] = curr.count;
      return acc;
    },
    {} as Record<string, number>
  );
  // Ensure the current activity is included in the map
  activityCountsMap[activityTypeKey] = newCount;

  const availableMilestones = await db
    .query('milestones')
    .withIndex('by_client_id', (q) => q.eq('clientId', clientId!))
    .filter((q) => q.eq(q.field('isEnabled'), true))
    .collect();

  const alreadyAchieved = await db
    .query('userMilestoneProgress')
    .withIndex('by_user_id', (q) => q.eq('userId', userId))
    .collect();

  const achievedMilestoneIds = new Set(
    alreadyAchieved.map((a) => a.milestoneId)
  );

  const milestoneEvaluation = MilestoneService.evaluateUserMilestones({
    activityCounts: activityCountsMap,
    availableMilestones,
    achievedMilestoneIds,
  });

  let milestonePointsAwarded = 0;
  if (milestoneEvaluation.newlyAchieved.length > 0) {
    milestonePointsAwarded = milestoneEvaluation.pointsAwarded;
    for (const milestone of milestoneEvaluation.newlyAchieved) {
      await db.insert('userMilestoneProgress', {
        userId,
        milestoneId: milestone._id,
        achievedAt: Date.now(),
        pointsEarned:
          milestone.rewards.find((r) => r.type === 'points')?.value || 0,
        badgesEarned: [], // Badge logic to be implemented
        milestoneName: milestone.name,
        milestoneDescription: milestone.description,
        milestoneIconUrl: milestone.iconUrl,
        milestoneConditions: milestone.conditions,
        isRepeatable: milestone.isRepeatable,
      });
    }
  }

  // 4. Update user points and evaluate tier
  const totalPointsEarned = activityPoints + milestonePointsAwarded;
  const newUserPoints = user.points + totalPointsEarned;
  await db.patch(userId, { points: newUserPoints });

  const tierEvaluation = await evaluateAndUpdateUserTier(
    ctx,
    userId,
    newUserPoints,
    user.tier,
    clientId!
  );

  // 5. Return comprehensive response
  return {
    success: true,
    pointsAwarded: totalPointsEarned,
    newMilestones: milestoneEvaluation.newlyAchieved.map((m) => ({
      name: m.name,
      points: m.rewards.find((r) => r.type === 'points')?.value || 0,
    })),
    tierAdvancement: tierEvaluation.hasAdvanced
      ? {
          previousTier: tierEvaluation.previousTier,
          newTier: tierEvaluation.newTier,
        }
      : null,
  };
};

// =========== Public-Facing Mutations ===========

/**
 * Logs a manual activity for the authenticated user.
 */
export const logActivity = mutation({
  args: {
    activityTypeKey: v.string(),
  },
  async handler(ctx, { activityTypeKey }) {
    const user = await requireAuthenticatedUser(ctx);
    if (!user.clientId) {
      throw new ConvexError(
        'User does not belong to a client and cannot log activities.'
      );
    }

    const activityType = await ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .filter((q) => q.eq(q.field('key'), activityTypeKey))
      .first();

    if (!activityType) {
      throw new ConvexError(
        `Activity type "${activityTypeKey}" not found for this client.`
      );
    }

    return _logActivityCore(ctx, user, activityType, 'manual');
  },
});

// =========== Internal Mutations ===========

/**
 * Internal mutation for logging activities from automated systems like webhooks or polling services.
 * It includes a check to prevent duplicate processing of the same external event.
 */
export const internalLogActivity = internalMutation({
  args: {
    userId: v.id('users'),
    activityTypeKey: v.string(),
    source: v.union(v.literal('webhook'), v.literal('api_poll')),
    externalReference: v.object({
      provider: v.string(),
      externalId: v.string(),
      externalTimestamp: v.optional(v.number()),
    }),
  },
  async handler(ctx, { userId, activityTypeKey, source, externalReference }) {
    // 1. Check for duplicate activity to ensure idempotency
    const existingActivity = await ctx.db
      .query('activities')
      .withIndex('by_external_reference', (q) =>
        q
          .eq('externalReference.provider', externalReference.provider)
          .eq('externalReference.externalId', externalReference.externalId)
      )
      .first();

    if (existingActivity) {
      console.log(
        `Activity already logged for external ID ${externalReference.externalId}. Skipping.`
      );
      return { success: true, duplicate: true };
    }

    // 2. Get user and activity type details
    const user = await ctx.db.get(userId);
    if (!user || !user.clientId) {
      throw new ConvexError('User not found or does not belong to a client.');
    }

    const activityType = await ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .filter((q) => q.eq(q.field('key'), activityTypeKey))
      .first();

    if (!activityType) {
      throw new ConvexError(
        `Activity type "${activityTypeKey}" not found for client ${user.clientId}.`
      );
    }

    // 3. Delegate to the core logic
    return _logActivityCore(ctx, user, activityType, source, externalReference);
  },
});

// =========== Queries ===========

/**
 * Gets recent activities for the authenticated user to display on the dashboard.
 */
export const getRecentActivities = query({
  async handler(ctx) {
    const user = await requireAuthenticatedUser(ctx);

    const activities = await ctx.db
      .query('activities')
      .withIndex('by_userId_timestamp', (q) => q.eq('userId', user._id))
      .order('desc')
      .take(10);

    if (!user.clientId) {
      return activities.map((a) => ({
        ...a,
        activityName: a.activityTypeKey || a.activityType || 'unknown',
        activityIcon: 'activity',
      }));
    }

    // Join with activityType to get the name and icon
    const activityTypes = await ctx.db
      .query('activityTypes')
      .withIndex('by_client_id', (q) => q.eq('clientId', user.clientId!))
      .collect();

    const activityTypesMap = new Map(
      activityTypes.map((at) => [
        at.key,
        {
          name: at.name,
          icon: at.icon || (at as any).iconUrl || 'activity', // Handle both icon field names
        },
      ])
    );

    return activities.map((a) => {
      // Handle both legacy 'activityType' and new 'activityTypeKey' fields
      const typeKey = a.activityTypeKey || a.activityType || 'unknown';
      return {
        ...a,
        activityName: activityTypesMap.get(typeKey)?.name || typeKey,
        activityIcon: activityTypesMap.get(typeKey)?.icon || 'activity',
      };
    });
  },
});

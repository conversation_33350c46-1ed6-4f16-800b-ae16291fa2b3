import { internalMutation } from '../_generated/server';

/**
 * One-time data migration to rename 'activityType' field to 'activityTypeKey' in activities table
 * This fixes legacy data that was created with the old field name.
 */
export const migrateActivityTypeField = internalMutation({
  async handler(ctx) {
    const activities = await ctx.db.query('activities').collect();

    let migratedCount = 0;

    for (const activity of activities) {
      // Check if activity has the legacy 'activityType' field but not 'activityTypeKey'
      if ('activityType' in activity && !activity.activityTypeKey) {
        await ctx.db.patch(activity._id, {
          activityTypeKey: (activity as any).activityType,
          activityType: undefined, // Remove the legacy field
        });
        migratedCount++;
      }
    }

    console.log(
      `Migrated ${migratedCount} activities from 'activityType' to 'activityTypeKey'`
    );
    return { migratedCount };
  },
});

/**
 * Fixes activities that have missing 'source' field by setting it to 'manual'
 */
export const fixMissingSourceField = internalMutation({
  async handler(ctx) {
    const activities = await ctx.db.query('activities').collect();

    let fixedCount = 0;

    for (const activity of activities) {
      if (!activity.source) {
        await ctx.db.patch(activity._id, {
          source: 'manual',
        });
        fixedCount++;
      }
    }

    console.log(`Fixed ${fixedCount} activities with missing 'source' field`);
    return { fixedCount };
  },
});

/**
 * Fixes activities that have missing 'pointsAwarded' field by setting default points
 */
export const fixMissingPointsAwardedField = internalMutation({
  async handler(ctx) {
    const activities = await ctx.db.query('activities').collect();

    let fixedCount = 0;

    for (const activity of activities) {
      if (
        activity.pointsAwarded === undefined ||
        activity.pointsAwarded === null
      ) {
        // Default to 10 points for legacy activities without points
        await ctx.db.patch(activity._id, {
          pointsAwarded: 10,
        });
        fixedCount++;
      }
    }

    console.log(
      `Fixed ${fixedCount} activities with missing 'pointsAwarded' field`
    );
    return { fixedCount };
  },
});

/**
 * Migrates activityTypes from 'iconUrl' field to 'icon' field
 */
export const migrateActivityTypesIconField = internalMutation({
  async handler(ctx) {
    const activityTypes = await ctx.db.query('activityTypes').collect();

    let migratedCount = 0;

    for (const activityType of activityTypes) {
      // Check if activityType has the legacy 'iconUrl' field but not 'icon'
      if ('iconUrl' in activityType && !activityType.icon) {
        await ctx.db.patch(activityType._id, {
          icon: (activityType as any).iconUrl,
          iconUrl: undefined, // Remove the legacy field
        });
        migratedCount++;
      }
    }

    console.log(
      `Migrated ${migratedCount} activityTypes from 'iconUrl' to 'icon'`
    );
    return { migratedCount };
  },
});

/**
 * Comprehensive migration that fixes all field issues in activities table
 */
export const migrateAllActivitiesFields = internalMutation({
  async handler(ctx) {
    const activities = await ctx.db.query('activities').collect();

    let migratedCount = 0;
    let fixedSourceCount = 0;
    let fixedPointsCount = 0;

    for (const activity of activities) {
      const updates: any = {};

      // Fix activityType -> activityTypeKey
      if ('activityType' in activity && !activity.activityTypeKey) {
        updates.activityTypeKey = (activity as any).activityType;
        updates.activityType = undefined; // Remove legacy field
        migratedCount++;
      }

      // Fix missing source field
      if (!activity.source) {
        updates.source = 'manual';
        fixedSourceCount++;
      }

      // Fix missing pointsAwarded field
      if (
        activity.pointsAwarded === undefined ||
        activity.pointsAwarded === null
      ) {
        updates.pointsAwarded = 10; // Default points for legacy activities
        fixedPointsCount++;
      }

      // Apply updates if any are needed
      if (Object.keys(updates).length > 0) {
        await ctx.db.patch(activity._id, updates);
      }
    }

    console.log(
      `Migration complete: ${migratedCount} activityType->activityTypeKey, ${fixedSourceCount} source fields, ${fixedPointsCount} pointsAwarded fields`
    );
    return {
      migratedActivityType: migratedCount,
      fixedSource: fixedSourceCount,
      fixedPoints: fixedPointsCount,
    };
  },
});

/**
 * Master migration function that fixes all schema issues across all tables
 */
export const migrateAllTables = internalMutation({
  async handler(ctx) {
    console.log('Starting comprehensive database migration...');

    // Migrate activities table
    const activitiesResult = await ctx.runMutation(
      'functions/dataMigration:migrateAllActivitiesFields'
    );

    // Migrate activityTypes table
    const activityTypesResult = await ctx.runMutation(
      'functions/dataMigration:migrateActivityTypesIconField'
    );

    console.log('All migrations complete!');
    return {
      activities: activitiesResult,
      activityTypes: activityTypesResult,
    };
  },
});

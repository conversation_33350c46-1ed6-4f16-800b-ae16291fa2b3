import { defineConfig, devices } from '@playwright/test';
import { STORAGE_STATE } from './tests/global.setup';
import * as dotenv from 'dotenv';

// Load environment variables from .env.test file
dotenv.config({ path: './.env.test' });

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 1 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [['html', { open: 'never' }]],

  /* Global timeout for each test - MUCH faster */
  timeout: 15 * 1000, // 15 seconds per test (was unlimited)

  /* Global timeout for expect() assertions - MUCH faster */
  expect: {
    timeout: 3 * 1000, // 3 seconds for assertions (was 5 seconds default)
  },

  /* Use global setup to authenticate once. */
  globalSetup: './tests/global.setup.ts',

  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:3000',

    /* Use saved storage state for authentication */
    storageState: STORAGE_STATE,

    /* MUCH faster action timeout */
    actionTimeout: 5 * 1000, // 5 seconds for actions (was 30 seconds default)

    /* MUCH faster navigation timeout */
    navigationTimeout: 10 * 1000, // 10 seconds for page loads (was 30 seconds default)

    /* Collect trace when retrying the failed test */
    trace: 'on-first-retry',

    /* Take screenshot on failure */
    screenshot: 'only-on-failure',

    /* Disable video recording for speed */
    video: 'off', // Changed from 'retain-on-failure' to 'off' for speed
  },

  /* Configure projects for major browsers - ONLY CHROMIUM for speed */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    // Disabled for speed - uncomment when needed
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },

    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },

    /* Test against mobile viewports. */
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    // {
    //   name: 'Mobile Safari',
    //   use: { ...devices['iPhone 12'] },
    // },

    /* Test against branded browsers. */
    // {
    //   name: 'Microsoft Edge',
    //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
    // },
    // {
    //   name: 'Google Chrome',
    //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
    // },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'pnpm dev:web',
    url: 'http://localhost:3000',
    reuseExistingServer: true,
    timeout: 30 * 1000, // 30 seconds for the dev server to start (was 2 minutes)
    stdout: 'ignore', // Don't show dev server output for cleaner test runs
    stderr: 'pipe', // Only show errors
  },
});

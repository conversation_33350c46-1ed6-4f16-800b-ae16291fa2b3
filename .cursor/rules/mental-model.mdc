---
description: 
globs: 
alwaysApply: true
---
# FitRewards Platform: System Overview & Mental Model

## 1. High-Level Summary

The Fitness Rewards Platform (FitRewards) is a comprehensive white-label gamification platform designed for fitness businesses including gyms, studios, and corporate wellness programs. The platform motivates users through a sophisticated rewards ecosystem featuring points, tier progression, milestone achievements, and customizable rewards catalogs. Built as a multi-tenant SaaS solution, it allows different clients to maintain their own branded experience with custom configurations while sharing a unified, scalable infrastructure.

## 2. Core Architectural Pattern

**Architecture Style:** Modern cloud-native monorepo with Backend-as-a-Service (BaaS) integration

**Key Design Principles:**

- **Multi-tenant by design:** Complete client isolation at the database level with client-specific configurations
- **Privacy-first approach:** Anonymous-first leaderboards and user interactions with optional identity revelation
- **Decoupled business logic:** Pure business logic isolated in tested service layer (`packages/core`)
- **Real-time by default:** Live data synchronization via Convex with optimistic UI updates
- **Error-resilient:** Comprehensive error boundaries, input validation, and graceful failure recovery
- **Mobile-first responsive:** Optimized for mobile devices with desktop enhancement

**Technology Foundation:**

- **Frontend:** React 18 SPA with TypeScript, Vite, and Tailwind CSS
- **Backend:** Convex serverless platform with structured logging and authentication helpers
- **Authentication:** Clerk with role-based access control and webhook-driven user synchronization
- **Monorepo:** pnpm workspaces with Turbo for optimized build pipelines
- **Testing:** Jest with 95%+ coverage for business logic layer

## 3. Key Components & Services

| Component                                | Responsibility                                                        | Technology                               |
| ---------------------------------------- | --------------------------------------------------------------------- | ---------------------------------------- |
| **Frontend App** (`apps/web`)            | React SPA with error boundaries, responsive UI, and real-time updates | React 18, TypeScript, Tailwind CSS, Vite |
| **Backend Services** (`packages/db`)     | Serverless functions, database schema, webhook handlers               | Convex, TypeScript                       |
| **Business Logic** (`packages/core`)     | Pure domain logic for milestones, tiers, and gamification rules       | TypeScript, Jest (95%+ coverage)         |
| **Authentication**                       | User management, role-based access control, organization sync         | Clerk with webhook integration           |
| **Shared Libraries** (`packages/shared`) | Common types, validation schemas, utilities                           | TypeScript, Zod                          |
| **Client Assets** (`clients/`)           | Tenant-specific branding, configurations, style guides                | Static assets, documentation             |

## 4. Data Flow

**Typical User Request Flow:**

1. **Authentication:** User authenticates via Clerk, JWT validated by Convex
2. **Authorization:** Role-based access control checks user permissions
3. **Data Query:** Convex functions query tenant-specific data with proper isolation
4. **Business Logic:** Core services (TierService, MilestoneService) process domain logic
5. **Real-time Updates:** Convex subscriptions push live updates to React components
6. **UI Response:** Optimistic updates with error boundaries and loading states

**Activity Logging Flow:**

1. User logs activity (manual or automated via API integrations)
2. Activity recorded in `activities` table with audit trail
3. MilestoneService evaluates new activity against client milestones
4. TierService calculates potential tier advancement based on points
5. Real-time notifications for achievements and tier progressions
6. Dashboard updates reflect new points, milestones, and tier status

## 5. Data Models

**Core Entities & Relationships:**

- **`users`** - User profiles with points, tier, role, and leaderboard settings

  - Links to `clientId` for multi-tenancy
  - Contains `leaderboardSettings` for anonymous-first privacy
  - Tracks `externalIds` for API integration matching

- **`clients`** - Tenant organizations with unique slugs and branding

  - One-to-many with users, activities, milestones, rewards

- **`clientConfiguration`** - Client-specific settings and feature flags

  - Branding colors, logos, avatar color palettes
  - Feature toggles (tiers, leaderboards, social sharing)
  - Dashboard layout customization

- **`activities`** - Event log of all user actions with audit trail

  - Source tracking (manual, webhook, API poll)
  - External system references for integration
  - Points awarded per activity

- **`milestones`** - Configurable achievement rules per client

  - Trigger conditions (activity count, type matching)
  - Reward definitions (points, badges)
  - Admin-manageable via self-service UI

- **`rewards`** - Point-redeemable items in client catalogs

  - Cost in points, descriptions, images
  - Admin-manageable with fulfillment tracking

- **`userRedemptions`** - Redemption transactions with fulfillment workflow

  - Status tracking (Pending → Fulfilled)
  - Staff fulfillment audit trail

- **`tierProgressions`** - Historical tier advancement tracking
  - Advancement dates, points at promotion
  - Analytics for tier system effectiveness

## 6. Authentication & Authorization

**Authentication Provider:** Clerk

- JWT-based authentication with automatic token validation
- Social login support and secure session management
- Webhook-driven user profile synchronization

**Authorization Model:** Role-Based Access Control (RBAC)

- **Roles:** `admin`, `staff`, `member` (default)
- **Admin:** Full client configuration access, user management, analytics
- **Staff:** Reward fulfillment, user search, redemption management
- **Member:** Personal dashboard, activity logging, reward redemption

**Multi-tenant Security:**

- All database queries filtered by `clientId`
- Role verification on every protected endpoint
- Client isolation enforced at the database level
- Webhook signature verification for external integrations

## 7. Key Dependencies

**Core Infrastructure:**

- **Convex** - Backend-as-a-Service platform for database, functions, and real-time subscriptions
- **Clerk** - Authentication and user management service
- **Vercel** - Frontend hosting and deployment platform

**Frontend Libraries:**

- **React Router v6** - Client-side routing with data loading
- **React Hook Form + Zod** - Form handling with type-safe validation
- **Tailwind CSS** - Utility-first styling with responsive design
- **Radix UI** - Accessible component primitives
- **Huge Icons** - Comprehensive icon library for customizable UI elements
- **Boring Avatars** - Algorithmic avatar generation for anonymous users

**Development & Quality:**

- **TypeScript** - Type safety across the entire stack
- **Jest** - Unit testing with high coverage requirements
- **ESLint + Prettier** - Code quality and formatting
- **Turbo** - Monorepo build orchestration and caching

---

## Detailed Feature Implementation Status

### ✅ Implemented Core Features

**Gamification Engine:**

- **Points System:** Users earn points for activities with configurable point values per activity type
- **Tier Progression:** Five-tier system (Bronze → Silver → Gold → Platinum → Diamond) with automatic advancement
- **Milestone System:** Admin-configurable achievements with activity count triggers and point rewards
- **Activity Tracking:** Comprehensive logging with manual and automated (API integration) sources

**Rewards & Fulfillment:**

- **Rewards Catalog:** Admin-managed catalog with point costs, descriptions, and images
- **Redemption System:** Atomic point deduction with pending/fulfilled status tracking
- **Staff Fulfillment:** Dedicated admin interface for staff to mark redemptions as fulfilled
- **Audit Trail:** Complete tracking of who fulfilled what and when

**User Experience:**

- **Anonymous-First Leaderboards:** Privacy-focused with beautiful generated avatars and names
- **User History Hub:** Comprehensive activity history and achievements gallery
- **Real-time Dashboard:** Live updates for points, tier progress, and recent activities
- **Mobile-Responsive Design:** Optimized for mobile-first usage patterns

**Admin Capabilities:**

- **Self-Service Milestone Management:** Full CRUD operations for client milestones
- **Rewards Management:** Complete catalog management with activation controls
- **User Search & Management:** Staff can search users and manage redemptions
- **Client Branding:** Customizable colors, logos, and avatar palettes

**Technical Infrastructure:**

- **Multi-tenant Architecture:** Complete client isolation with shared infrastructure
- **API Integrations:** Automated activity logging from external systems (MarianaTek, MindBody)
- **Error Handling:** Comprehensive error boundaries and graceful failure recovery
- **Testing Coverage:** 95%+ test coverage for business logic layer

### 🚧 Planned Features (Based on PRDs)

**Enhanced Social Features:**

- **Cheering System:** Anonymous social interactions between users
- **Advanced Leaderboards:** Filtering, sorting, and competitive elements
- **Social Sharing:** Integration with external social media platforms

**Advanced Gamification:**

- **Streak Tracking:** Consecutive activity tracking and rewards
- **Seasonal Challenges:** Time-limited milestone campaigns
- **Team Competitions:** Group-based challenges and leaderboards

**Business Intelligence:**

- **Analytics Dashboard:** Client engagement and retention metrics
- **A/B Testing Framework:** Feature experimentation capabilities
- **Revenue Analytics:** Tier-based monetization insights

**Integration Expansion:**

- **Additional Fitness Platforms:** Expanded API integration support
- **Email Marketing:** Automated engagement campaigns
- **Mobile Push Notifications:** Native app notification system

---

## System Architecture Diagrams

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        Users[End Users]
        Admins[Client Admins]
        Staff[Staff Members]
    end

    subgraph "Frontend (Vercel)"
        WebApp[React SPA<br/>apps/web]
        ErrorBoundaries[Error Boundaries]
        RealTimeUI[Real-time UI Updates]
        ResponsiveDesign[Mobile-First Design]
    end

    subgraph "Authentication (Clerk)"
        ClerkAuth[Clerk Service]
        Webhooks[User Sync Webhooks]
        RBAC[Role-Based Access]
    end

    subgraph "Backend (Convex)"
        ConvexAPI[Convex API Layer]
        ConvexDB[(Convex Database)]

        subgraph "Business Logic"
            CoreServices[Core Services<br/>packages/core]
            TierService[TierService]
            MilestoneService[MilestoneService]
        end

        subgraph "Convex Functions"
            UserFunctions[User Management]
            ActivityFunctions[Activity Logging]
            RewardFunctions[Rewards & Redemptions]
            AdminFunctions[Admin Operations]
        end
    end

    subgraph "External Integrations"
        MarianaTek[MarianaTek API]
        MindBody[MindBody API]
        EmailService[Email Service]
    end

    Users --> WebApp
    Admins --> WebApp
    Staff --> WebApp

    WebApp --> ClerkAuth
    WebApp --> ConvexAPI

    ClerkAuth --> Webhooks
    Webhooks --> ConvexAPI

    ConvexAPI --> UserFunctions
    ConvexAPI --> ActivityFunctions
    ConvexAPI --> RewardFunctions
    ConvexAPI --> AdminFunctions

    UserFunctions --> CoreServices
    ActivityFunctions --> CoreServices
    RewardFunctions --> CoreServices

    CoreServices --> TierService
    CoreServices --> MilestoneService

    ConvexAPI --> ConvexDB

    ConvexAPI --> MarianaTek
    ConvexAPI --> MindBody
    ConvexAPI --> EmailService
```

### Data Model Relationships

```mermaid
erDiagram
    CLIENTS ||--o{ USERS : "belongs to"
    CLIENTS ||--o{ CLIENT_CONFIGURATION : "has"
    CLIENTS ||--o{ MILESTONES : "defines"
    CLIENTS ||--o{ REWARDS : "offers"
    CLIENTS ||--o{ ACTIVITY_TYPES : "configures"

    USERS ||--o{ ACTIVITIES : "performs"
    USERS ||--o{ USER_REDEMPTIONS : "makes"
    USERS ||--o{ USER_MILESTONE_PROGRESS : "achieves"
    USERS ||--o{ TIER_PROGRESSIONS : "advances"

    MILESTONES ||--o{ USER_MILESTONE_PROGRESS : "tracked in"
    REWARDS ||--o{ USER_REDEMPTIONS : "redeemed as"

    CLIENTS {
        string name
        string slug
        number createdAt
    }

    USERS {
        string clerkUserId
        string email
        string firstName
        string lastName
        id clientId
        number points
        string tier
        string role
        object leaderboardSettings
        array externalIds
    }

    ACTIVITIES {
        id userId
        id clientId
        string activityType
        number timestamp
        number pointsAwarded
        string source
        object externalReference
    }

    MILESTONES {
        id clientId
        string name
        string description
        string iconUrl
        string triggerType
        object conditions
        array rewards
        boolean isEnabled
        boolean isRepeatable
    }

    REWARDS {
        id clientId
        string name
        string description
        number cost
        string imageUrl
        boolean isActive
    }
```

### User Journey Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Clerk
    participant Convex
    participant CoreServices

    Note over User,CoreServices: New User Onboarding
    User->>Frontend: Sign up
    Frontend->>Clerk: Create account
    Clerk-->>Frontend: User created
    Clerk->>Convex: Webhook: user.created
    Convex->>Convex: Create user record

    Note over User,CoreServices: Activity Logging & Gamification
    User->>Frontend: Log activity
    Frontend->>Convex: logActivity mutation
    Convex->>Convex: Record activity
    Convex->>CoreServices: Evaluate milestones
    CoreServices-->>Convex: Milestone results
    Convex->>CoreServices: Calculate tier
    CoreServices-->>Convex: Tier progression
    Convex-->>Frontend: Real-time updates
    Frontend-->>User: Achievement notifications

    Note over User,CoreServices: Reward Redemption
    User->>Frontend: Browse rewards
    Frontend->>Convex: Get active rewards
    User->>Frontend: Redeem reward
    Frontend->>Convex: Atomic redemption
    Convex->>Convex: Deduct points + create redemption
    Convex-->>Frontend: Success confirmation

    Note over User,CoreServices: Staff Fulfillment
    User->>Staff: Request reward
    Staff->>Frontend: Search user
    Frontend->>Convex: Find pending redemptions
    Staff->>Frontend: Mark as fulfilled
    Frontend->>Convex: Update redemption status
    Convex-->>Frontend: Audit trail updated
```

---

## Development Guidelines

### Code Organization Principles

**Monorepo Structure:**

```
fitness-rewards-platform/
├── apps/web/                 # React frontend application
├── packages/core/            # Pure business logic (95%+ test coverage)
├── packages/db/              # Convex backend functions and schema
├── packages/shared/          # Common types and utilities
├── clients/                  # Client-specific assets and configurations
└── docs/                     # Documentation and PRDs
```

**Key Development Patterns:**

1. **Error-First Design:** Every component includes error boundaries and graceful failure handling
2. **Type Safety:** Comprehensive TypeScript usage with strict configuration
3. **Testing Strategy:** Business logic in `packages/core` requires 95%+ test coverage
4. **Real-time Updates:** Leverage Convex subscriptions for live data synchronization
5. **Mobile-First:** All UI components designed for mobile with desktop enhancement
6. **Privacy by Default:** Anonymous-first approach for all social features

### Performance Considerations

**Frontend Optimization:**

- Lazy loading for route-based code splitting
- Optimistic UI updates with error rollback
- Efficient re-rendering with proper React patterns
- Mobile-optimized bundle sizes

**Backend Efficiency:**

- Database queries optimized with proper indexing
- Multi-tenant data isolation without performance impact
- Caching strategies for frequently accessed data
- Webhook processing with retry mechanisms

**Scalability Patterns:**

- Horizontal scaling through serverless architecture
- Client isolation enabling independent scaling
- Stateless business logic for easy distribution
- Event-driven architecture for loose coupling

---

## Summary

The FitRewards Platform represents a mature, production-ready gamification solution built with modern cloud-native architecture. Its multi-tenant design, comprehensive feature set, and privacy-first approach make it an ideal solution for fitness businesses looking to increase member engagement and retention.

**Key Strengths:**

- **Proven Architecture:** Battle-tested with real clients and comprehensive error handling
- **Developer Experience:** Type-safe, well-tested codebase with excellent tooling
- **Business Value:** Complete gamification loop from activity tracking to reward fulfillment
- **Scalability:** Serverless architecture supporting multiple clients with isolated data
- **Privacy Focus:** Anonymous-first design building user trust and participation

**Technical Excellence:**

- 95%+ test coverage for business logic
- Real-time updates with optimistic UI patterns
- Comprehensive error boundaries and graceful degradation
- Mobile-first responsive design
- Automated CI/CD with quality gates

This system overview serves as the foundational context for AI assistants working on the platform, providing the necessary architectural understanding to make informed decisions and maintain consistency with established patterns.

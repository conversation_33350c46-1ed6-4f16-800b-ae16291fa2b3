import { useMutation, useQuery } from 'convex/react';
import { api } from '@fitness-rewards/db';
import { LogActivityButton } from '../components/user/LogActivityButton';
import TierWidget from '../components/TierWidget';
import { useEffect } from 'react';
import { WithLoading } from '../components/utils/WithLoading';
import { formatActivityType } from '../utils';
import { HugeIcon } from '@/components/icons/HugeIcon';
import { LeaderboardSettings } from '../components/user/LeaderboardSettings';
import { Link } from 'react-router-dom';
import { Doc } from '@fitness-rewards/db/dataModel';

/**
 * Entry point for the Dashboard. Handles initial user loading and creation.
 */
export default function DashboardPage() {
  const user = useQuery(api.functions.users.getCurrentUser);
  const getOrCreateUser = useMutation(api.functions.users.getOrCreateUser);

  // Only fetch other data if we have a user
  const skipQueries = user === undefined || user === null;
  const activities = useQuery(
    api.functions.activities.getRecentActivities,
    skipQueries ? 'skip' : {}
  );
  const milestones = useQuery(
    api.functions.users.getUserMilestones,
    skipQueries ? 'skip' : {}
  );
  const tierProgress = useQuery(
    api.functions.users.getUserTierProgress,
    skipQueries ? 'skip' : {}
  );
  const tierStats = useQuery(
    api.functions.users.getTierStatistics,
    skipQueries ? 'skip' : {}
  );

  useEffect(() => {
    // If the user is logged in but doesn't have a DB record yet, create one.
    if (user === null) {
      getOrCreateUser().catch(console.error);
    }
  }, [user, getOrCreateUser]);

  // Show loading while user is being fetched or created
  if (user === undefined) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 text-center">
          <WithLoading isPending={true} spinnerSize="sm">
            <h1 className="text-3xl font-bold">Loading your Dashboard...</h1>
          </WithLoading>
          <p className="text-gray-600">Track your progress and earn rewards.</p>
        </div>
      </div>
    );
  }

  if (user === null) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold">Setting up your account...</h1>
          <p className="text-gray-600">Please wait a moment.</p>
        </div>
      </div>
    );
  }

  const formatRelativeTime = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor(diff / (1000 * 60));

    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  };

  /**
   * Helper function to format date for achievements
   * @param timestamp - Unix timestamp
   * @returns Formatted date string
   */
  const formatAchievementDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold">
          Welcome, {user.firstName || 'Fitness Enthusiast'}!
        </h1>
        <p className="text-gray-600">Track your progress and earn rewards.</p>
      </div>

      <div className="mb-8 flex justify-center">
        <LogActivityButton />
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <WithLoading
            isPending={tierProgress === undefined || tierStats === undefined}
          >
            {tierProgress && tierStats && (
              <TierWidget tierProgress={tierProgress} tierStats={tierStats} />
            )}
          </WithLoading>
        </div>

        <div className="space-y-6">
          <div className="rounded-lg bg-white p-6 shadow-md">
            <h2 className="mb-4 text-xl font-semibold">Quick Stats</h2>
            <div className="text-4xl font-bold text-indigo-600">
              {user.points.toLocaleString()}
            </div>
            <p className="mt-2 text-sm text-gray-500">Total Points Earned</p>
          </div>
          <LeaderboardSettings user={user} />
        </div>

        {/* Enhanced Recent Activities Section */}
        <div className="rounded-lg bg-white p-6 shadow-md md:col-span-2">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Recent Activities
          </h2>
          <WithLoading isPending={activities === undefined}>
            {activities && activities.length === 0 ? (
              <div className="py-6 text-center">
                <div className="mx-auto mb-3 h-12 w-12 text-gray-400">
                  <HugeIcon
                    icon="calendar-check-in-01"
                    className="h-full w-full opacity-50"
                  />
                </div>
                <p className="text-sm text-gray-500">No activities yet.</p>
                <p className="mt-1 text-xs text-gray-400">
                  Start your fitness journey by logging your first activity!
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {activities?.slice(0, 5).map((activity) => (
                  <div
                    key={activity._id}
                    className="flex items-center space-x-3 rounded-lg bg-gray-50 p-3 transition-colors hover:bg-gray-100"
                  >
                    <div className="flex-shrink-0">
                      <HugeIcon
                        icon={activity.activityIcon}
                        className="h-8 w-8 text-indigo-600"
                      />
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium text-gray-900">
                        {activity.activityName}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatRelativeTime(activity.timestamp)}
                      </p>
                    </div>
                    <div className="flex-shrink-0 text-right">
                      <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                        +{activity.pointsAwarded} pts
                      </span>
                    </div>
                  </div>
                ))}
                {activities && activities.length > 5 && (
                  <div className="pt-2 text-center">
                    <Link
                      to="/history/activities"
                      className="text-xs font-medium text-indigo-600 hover:text-indigo-800"
                    >
                      View all activities →
                    </Link>
                  </div>
                )}
              </div>
            )}
          </WithLoading>
        </div>

        {/* Enhanced Achievements Section */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">
            Recent Achievements
          </h2>
          <WithLoading isPending={milestones === undefined}>
            {milestones && milestones.length === 0 ? (
              <div className="py-6 text-center">
                <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center text-gray-400">
                  <HugeIcon
                    icon="award-01"
                    className="h-full w-full opacity-50"
                  />
                </div>
                <p className="text-sm text-gray-500">No achievements yet.</p>
                <p className="mt-1 text-xs text-gray-400">
                  Complete activities to unlock your first achievement!
                </p>
              </div>
            ) : (
              <ul className="space-y-4">
                {milestones?.slice(0, 3).map((milestone) => (
                  <li
                    key={milestone._id}
                    className="flex items-start space-x-3"
                  >
                    <div className="flex-shrink-0">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-amber-500">
                        <HugeIcon
                          icon={milestone.milestoneIconUrl}
                          className="h-5 w-5 text-white"
                        />
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="truncate text-sm font-medium text-gray-900">
                        {milestone.milestoneName}
                      </p>
                      <div className="mt-1 flex flex-wrap items-center gap-2">
                        {milestone.badgesEarned.map((badge) => (
                          <span
                            key={badge}
                            className="inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
                          >
                            <HugeIcon icon="medal-01" className="h-3 w-3" />
                            {badge}
                          </span>
                        ))}
                        {milestone.isRepeatable && (
                          <span className="inline-flex items-center gap-1 rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-600">
                            <HugeIcon icon="repeat" className="h-3 w-3" />
                            Repeatable
                          </span>
                        )}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </WithLoading>
        </div>
      </div>
    </div>
  );
}

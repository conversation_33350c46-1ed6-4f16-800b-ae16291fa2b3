import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '@db';
import { WithLoading } from '../utils/WithLoading';
import { Gift } from 'lucide-react';

export const RedemptionHistory: React.FC = () => {
  const history = useQuery(api.functions.userRedemptions.getHistory);

  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm">
      <h3 className="text-lg font-semibold text-gray-900">
        Your Redemption History
      </h3>
      <WithLoading isPending={history === undefined}>
        {history && history.length === 0 ? (
          <div className="mt-4 py-8 text-center text-gray-500">
            <p>You haven't redeemed any rewards yet.</p>
          </div>
        ) : (
          <ul role="list" className="mt-6 space-y-4">
            {history?.map((item) => (
              <li key={item._id} className="flex items-center space-x-4">
                <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-indigo-500 text-white">
                  <Gift className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <p className="font-semibold text-gray-800">
                    {item.rewardName}
                  </p>
                  <p className="text-sm text-gray-500">
                    {new Date(item.redemptionTimestamp).toLocaleDateString(
                      'en-US',
                      {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      }
                    )}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-red-600">
                    -{item.pointsSpent?.toLocaleString()} pts
                  </p>
                </div>
              </li>
            ))}
          </ul>
        )}
      </WithLoading>
    </div>
  );
};

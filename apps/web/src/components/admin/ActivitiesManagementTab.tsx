import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import toast from 'react-hot-toast';
import { api } from '@db';
import { Doc, Id } from '@db/types';
import { HugeIcon } from '../icons/HugeIcon';
import { WithLoading } from '../utils/WithLoading';
import ActivityTypeFormModal from './ActivityTypeFormModal';

/**
 * A tab component within the admin dashboard for managing client-specific activity types.
 * Allows admins to perform CRUD operations on activities their members can log.
 */
const ActivitiesManagementTab = () => {
  const activityTypes = useQuery(api.functions.activityTypes.getForClient);
  const deleteActivityType = useMutation(api.functions.activityTypes.del);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedActivityType, setSelectedActivityType] = useState<
    Doc<'activityTypes'> | undefined
  >(undefined);

  const handleOpenCreateModal = () => {
    setSelectedActivityType(undefined);
    setIsModalOpen(true);
  };

  const handleOpenEditModal = (activityType: Doc<'activityTypes'>) => {
    setSelectedActivityType(activityType);
    setIsModalOpen(true);
  };

  const handleDelete = (activityTypeId: Id<'activityTypes'>) => {
    if (
      window.confirm(
        'Are you sure you want to delete this activity type? This will fail if the activity is used in any milestones.'
      )
    ) {
      toast.promise(deleteActivityType({ id: activityTypeId }), {
        loading: 'Deleting activity type...',
        success: 'Activity type deleted!',
        error: (err) => err.data?.message || 'Failed to delete activity type.',
      });
    }
  };

  return (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Manage Activities</h2>
          <p className="text-gray-500">
            Define the activities members can log to earn points.
          </p>
        </div>
        <button
          onClick={handleOpenCreateModal}
          className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
        >
          <HugeIcon icon="add-circle" className="mr-2 h-5 w-5" />
          Create Activity
        </button>
      </div>

      <WithLoading isPending={activityTypes === undefined}>
        <div className="overflow-x-auto rounded-lg border">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="w-16 px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Icon
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Key
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  Points
                </th>
                <th className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {activityTypes && activityTypes.length === 0 ? (
                <tr>
                  <td
                    colSpan={5}
                    className="px-6 py-4 text-center text-sm text-gray-500"
                  >
                    No activity types created yet.
                  </td>
                </tr>
              ) : (
                activityTypes?.map((activityType) => (
                  <tr key={activityType._id}>
                    <td className="px-6 py-4">
                      <HugeIcon
                        icon={activityType.icon || 'activity'}
                        className="h-6 w-6 text-gray-600"
                      />
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                      {activityType.name}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      <code>{activityType.key}</code>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                      {activityType.points.toLocaleString()}
                    </td>
                    <td className="space-x-4 whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <button
                        onClick={() => handleOpenEditModal(activityType)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Edit"
                      >
                        <HugeIcon icon="edit-02" className="h-5 w-5" />
                        <span className="sr-only">Edit</span>
                      </button>
                      <button
                        onClick={() => handleDelete(activityType._id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete"
                      >
                        <HugeIcon icon="delete-02" className="h-5 w-5" />
                        <span className="sr-only">Delete</span>
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </WithLoading>

      <ActivityTypeFormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        activityType={selectedActivityType}
      />
    </div>
  );
};

export default ActivitiesManagementTab;

/**
 * @file Admin component for managing client-specific email branding.
 */
import { useMemo } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { api } from '../../api';
import { Id } from '@fitness-rewards/db/dataModel';
import { MemberRedemptionEmail } from '@fitness-rewards/emails';
import { createTheme, defaultTheme } from '@fitness-rewards/emails';
import { toast } from 'sonner';

// Zod schema for form validation
const emailBrandingSchema = z.object({
  logoUrl: z.string().url().optional().or(z.literal('')),
  primaryColor: z
    .string()
    .regex(/^#[0-9a-fA-F]{6}$/)
    .optional(),
  secondaryColor: z
    .string()
    .regex(/^#[0-9a-fA-F]{6}$/)
    .optional(),
  accentColor: z
    .string()
    .regex(/^#[0-9a-fA-F]{6}$/)
    .optional(),
  backgroundColor: z
    .string()
    .regex(/^#[0-9a-fA-F]{6}$/)
    .optional(),
  footerText: z.string().optional(),
});

type EmailBrandingFormData = z.infer<typeof emailBrandingSchema>;

const ColorInput = ({ name, control, label }) => (
  <div>
    <label htmlFor={name} className="block text-sm font-medium text-gray-700">
      {label}
    </label>
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <div className="mt-1 flex items-center gap-2">
          <input
            {...field}
            type="color"
            className="h-8 w-8 rounded border border-gray-300 p-1"
          />
          <input
            {...field}
            type="text"
            className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            placeholder="#6A4FF2"
          />
        </div>
      )}
    />
  </div>
);

/**
 * Main component for email branding settings.
 * Renders a form for admins to update their email branding and shows a live preview.
 * @returns {React.ReactElement} The email branding settings component.
 */
const EmailBrandingSettings = () => {
  const user = useQuery(api.functions.users.getCurrentUser);
  const clientConfig = useQuery(
    api.functions.clientConfigs.getClientConfiguration
  );
  const updateConfig = useMutation(api.functions.clientConfigs.updateConfig);

  const defaultValues = useMemo(
    () => ({
      logoUrl: clientConfig?.emailBranding?.logoUrl ?? '',
      primaryColor:
        clientConfig?.emailBranding?.primaryColor ??
        defaultTheme.colors.primary,
      secondaryColor:
        clientConfig?.emailBranding?.secondaryColor ??
        defaultTheme.colors.secondary,
      accentColor:
        clientConfig?.emailBranding?.accentColor ?? defaultTheme.colors.accent,
      backgroundColor:
        clientConfig?.emailBranding?.backgroundColor ??
        defaultTheme.colors.background,
      footerText:
        clientConfig?.emailBranding?.footerText ?? defaultTheme.footerText,
    }),
    [clientConfig]
  );

  const {
    control,
    watch,
    handleSubmit,
    formState: { isDirty, isSubmitting },
  } = useForm<EmailBrandingFormData>({
    resolver: zodResolver(emailBrandingSchema),
    defaultValues,
  });

  const formData = watch();
  const previewTheme = useMemo(() => createTheme(formData), [formData]);

  const onSubmit = async (data: EmailBrandingFormData) => {
    if (!user?.clientId) return;
    toast.promise(
      updateConfig({
        clientId: user.clientId as Id<'clients'>,
        config: { emailBranding: data },
      }),
      {
        loading: 'Saving branding...',
        success: 'Branding saved successfully!',
        error: 'Failed to save branding.',
      }
    );
  };

  if (!user || !clientConfig) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <h2 className="text-2xl font-bold">Email Branding</h2>
      <p className="mt-2 text-gray-500">
        Customize the look and feel of the emails sent to your members.
      </p>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="mt-6 grid grid-cols-1 gap-12 lg:grid-cols-2"
      >
        {/* Form Column */}
        <div className="space-y-6">
          <div>
            <label
              htmlFor="logoUrl"
              className="block text-sm font-medium text-gray-700"
            >
              Logo URL
            </label>
            <Controller
              name="logoUrl"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  id="logoUrl"
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="https://your-logo.com/logo.png"
                />
              )}
            />
          </div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <ColorInput
              name="primaryColor"
              control={control}
              label="Primary Color"
            />
            <ColorInput
              name="secondaryColor"
              control={control}
              label="Secondary Color"
            />
            <ColorInput
              name="accentColor"
              control={control}
              label="Accent Color"
            />
            <ColorInput
              name="backgroundColor"
              control={control}
              label="Background Color"
            />
          </div>
          <div>
            <label
              htmlFor="footerText"
              className="block text-sm font-medium text-gray-700"
            >
              Footer Text (supports Markdown)
            </label>
            <Controller
              name="footerText"
              control={control}
              render={({ field }) => (
                <textarea
                  {...field}
                  id="footerText"
                  rows={4}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  placeholder="Your Company, 123 Main St..."
                />
              )}
            />
          </div>
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={!isDirty || isSubmitting}
              className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
        {/* Preview Column */}
        <div className="rounded-lg bg-gray-100 p-4">
          <h3 className="mb-4 text-center text-lg font-medium">Live Preview</h3>
          <div className="overflow-hidden rounded-md bg-white shadow-md">
            <div
              style={{ transform: 'scale(0.9)', transformOrigin: 'top center' }}
            >
              <MemberRedemptionEmail
                theme={previewTheme}
                userFirstName="Alex"
                rewardName="Free Smoothie"
                pointsSpent={500}
                newPointBalance={1250}
                redemptionHistoryUrl="#"
              />
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default EmailBrandingSettings;

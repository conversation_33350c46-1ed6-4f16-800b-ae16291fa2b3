import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from 'convex/react';
import toast from 'react-hot-toast';

import { api } from '@db';
import { Doc } from '@db/types';
import {
  ActivityTypeCreate,
  activityTypeCreateSchema,
} from '@fitness-rewards/shared/schemas/activityTypeSchemas';

import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from '../ui/dialog';
import IconSearchDropdown from './IconSearchDropdown';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';

interface ActivityTypeFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  activityType?: Doc<'activityTypes'>;
}

const toSnakeCase = (str: string) => {
  return str
    .toLowerCase()
    .replace(/\s+/g, '_')
    .replace(/[^a-z0-9_]/g, '');
};

const ActivityTypeFormModal: React.FC<ActivityTypeFormModalProps> = ({
  isOpen,
  onClose,
  activityType,
}) => {
  const createActivityType = useMutation(api.functions.activityTypes.create);
  const updateActivityType = useMutation(api.functions.activityTypes.update);

  const isEditing = !!activityType;

  const form = useForm<ActivityTypeCreate>({
    resolver: zodResolver(activityTypeCreateSchema),
    defaultValues: {
      name: '',
      key: '',
      points: 10,
      iconName: 'activity',
    },
  });

  const nameValue = form.watch('name');

  useEffect(() => {
    if (isOpen) {
      if (isEditing) {
        form.reset({
          name: activityType.name,
          key: activityType.key,
          points: activityType.points,
          iconName: activityType.icon || 'activity',
        });
      } else {
        form.reset();
      }
    }
  }, [activityType, isOpen, isEditing, form]);

  useEffect(() => {
    if (!form.formState.isDirty && nameValue) {
      form.setValue('key', toSnakeCase(nameValue), { shouldValidate: true });
    }
  }, [nameValue, form]);

  const onSubmit = async (values: ActivityTypeCreate) => {
    const mutationPromise = isEditing
      ? updateActivityType({ id: activityType._id, ...values })
      : createActivityType(values);

    await toast.promise(mutationPromise, {
      loading: `${isEditing ? 'Updating' : 'Creating'} activity type...`,
      success: `Activity type successfully ${isEditing ? 'updated' : 'created'}!`,
      error: (err) =>
        err.data?.message ||
        `Failed to ${isEditing ? 'update' : 'create'} activity type.`,
    });

    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Activity Type' : 'Create New Activity Type'}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Personal Training" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="key"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Key</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., personal_training" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="points"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Points</FormLabel>
                  <FormControl>
                    <Input type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="iconName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon</FormLabel>
                  <FormControl>
                    <IconSearchDropdown
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Search for an icon..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="ghost">
                  Cancel
                </Button>
              </DialogClose>
              <Button type="submit" disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default ActivityTypeFormModal;

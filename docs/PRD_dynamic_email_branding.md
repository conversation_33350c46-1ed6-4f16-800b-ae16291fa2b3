# PRD: Dynamic, Client-Branded Email Notification System

**Author:** <PERSON> (Product Manager)
**Version:** 2.0
**Status:** Ready for Development

## 1. Introduction

### 1.1. Problem Statement

The FitRewards platform currently sends basic transactional emails for events like reward redemptions. However, these emails lack deep, client-specific branding (colors, fonts, logos) and are not built on a scalable framework that allows for the easy creation of new email types (e.g., tier advancements, milestone achievements). For a white-label SaaS product, providing a seamless brand experience is paramount. A generic email from "FitRewards" breaks the brand immersion for the end-user and diminishes the value proposition for our clients.

### 1.2. Proposed Solution

This document outlines a plan to create a centralized and scalable email templating system using **React Email**. This system will allow for the dynamic injection of client-specific branding into all outgoing emails. We will refactor the existing email structure to be more modular, separating templates, shared components, and branding themes. This will enable developers to rapidly create new, fully-branded emails and empower clients to deliver a professional and cohesive experience to their members.

### 1.3. Goals & Strategy

- **Enable Client-Specific Branding:** Allow every email to be styled with a client's unique logo, colors, and footer text.
- **Improve Developer Experience:** Create a simple, reusable, and type-safe framework for building new email templates without duplicating styling logic.
- **Enhance Member Experience:** Ensure members receive beautiful, professional emails that feel like they come directly from their trusted fitness studio.
- **Increase Platform Value:** Position our email system as a premium, white-label feature for our business clients.

### 1.4. Success Vision

Upon launch, clients can configure their email branding in a self-service manner within 5 minutes. All transactional emails will perfectly reflect the client's brand identity, creating a seamless and professional member experience. Developers can scaffold new, fully-branded emails in under an hour using a library of reusable, themed components. This feature will be highlighted in sales demos as a key differentiator of our white-label platform.

---

## 2. Target Audience

- **Client Administrator (e.g., David):**
  - **Goal:** "I want our members to feel like every part of their experience is from us, not some third-party app."
  - **Frustration:** "Generic emails make our premium service feel cheap and break the illusion of a fully branded experience."
- **End User / Member (e.g., Sarah):**
  - **Goal:** "I want to trust the emails I receive about my account and have them be instantly recognizable."
  - **Frustration:** "It's confusing to get an email from a company I don't recognize ('FitRewards') about my gym membership."
- **Platform Developer:**
  - **Goal:** "I want to build new emails quickly without having to write custom CSS for every single one or worry about brand consistency."
  - **Frustration:** "Reusing code for emails is painful, and maintaining brand consistency across a dozen templates is a nightmare."

---

## 3. User Stories

| ID       | User Story                                                                                                                                                                                    | Role      | Priority  |
| -------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------- | --------- |
| **BR-1** | As an admin, I want to configure my brand's logo, primary color, and secondary color in the admin dashboard to be used in all emails.                                                         | Admin     | Must-Have |
| **BR-2** | As a member, I want confirmation emails to look like they came from my studio, using their logo and colors, so I trust the communication.                                                     | Member    | Must-Have |
| **BR-3** | As a developer, I want to use a single, parameterized template for a specific event (like redemption) that automatically applies the correct client branding.                                 | Developer | Must-Have |
| **BR-4** | As a developer, I want to easily create a new type of email (e.g., "Tier Advancement") that automatically inherits all the branding capabilities.                                             | Developer | High      |
| **BR-5** | As an admin, I want to define custom footer text for emails to include links to our social media or specific studio policies.                                                                 | Admin     | High      |
| **BR-6** | As the system, when a client has not configured their email branding, I must use a professional and aesthetically pleasing default theme so that emails still look good.                      | System    | Must-Have |
| **BR-7** | As a developer, when an email fails to send due to a template rendering error, I want the system to log the detailed error with the branding props used so I can debug the issue effectively. | Developer | High      |
| **BR-8** | As an admin, I want to see a live preview of my branding changes on a sample email template before saving so I can be confident in my choices.                                                | Admin     | High      |

---

## 4. Functional Requirements

### 4.1. Database Schema Changes (`packages/db/convex/schema.ts`)

- **FR1: Update `clientConfiguration` Schema:** The `clientConfiguration` table in `packages/db/convex/schema.ts` will be updated to include a structured `emailBranding` object.

  ```typescript
  // In clientConfiguration table definition
  emailBranding: v.optional(
    v.object({
      logoUrl: v.optional(v.string()),
      primaryColor: v.optional(v.string()),
      secondaryColor: v.optional(v.string()),
      accentColor: v.optional(v.string()),
      backgroundColor: v.optional(v.string()),
      footerText: v.optional(v.string()), // Supports basic markdown
    })
  ),
  ```

### 4.2. Admin UI (`apps/web/`)

- **FR2: New Admin Settings Component:** A new component, `apps/web/src/components/admin/EmailBrandingSettings.tsx`, will be created and placed within a "Branding" tab in the admin dashboard.
- **FR3: Branding Form:** The component will use `react-hook-form` and a Zod schema for type-safe validation. It will contain fields for all properties in the `emailBranding` object.
- **FR4: Live Preview:** The form page will include a live preview pane that renders a sample email template (e.g., `MemberRedemptionEmail`) and passes the current (unsaved) form state to it, allowing admins to see their changes in real-time.
- **FR5: Color Validation:** The UI will provide live feedback on color choices, such as warnings for poor color contrast ratios (e.g., light text on a light background).

### 4.3. Email Package Architecture (`packages/emails/`)

- **FR6: Refined Folder Structure:**
  ```
  packages/emails/src/
  ├── components/         # Shared, themeable React Email components (e.g., BrandedHeader, Footer, BrandedButton)
  ├── templates/          # Specific email templates (e.g., MemberRedemptionEmail.tsx)
  ├── themes/             # Branding definitions, types, and theme creation logic
  └── lib/                # Core email sending logic
  ```
- **FR7: Centralized `EmailBranding` Type:** A new file, `packages/emails/src/themes/types.ts`, will define the `EmailBranding` interface and a more comprehensive `EmailTheme` interface.
- **FR8: Theming Engine:**
  - A `defaultTheme.ts` will provide fallback branding values to ensure all emails are well-styled.
  - A utility function `createTheme(branding: Partial<EmailBranding>): EmailTheme` will merge the client's branding configuration with the default theme. This ensures that even partial configurations result in a complete, coherent theme.
- **FR9: Template Refactoring:** All email templates (starting with `MemberRedemptionEmail` and `StaffNotificationEmail`) will be refactored to accept a single `theme: EmailTheme` prop. All inline styles will be derived from this theme object.

### 4.4. Backend Logic (`packages/db/`)

- **FR10: Centralized Email Action:** A new internal action `emails:send` will be created. It will be the single entry point for all email-sending operations.
  - **Signature:** `(ctx, args: { to: string; template: string; props: Record<string, any>; userId: Id<"users"> })`
  - **Logic:**
    1. Look up the user and their associated `clientId`.
    2. Fetch the `clientConfiguration` for that `clientId`.
    3. Use the `createTheme` utility to generate the final `EmailTheme` from `clientConfiguration.emailBranding`.
    4. Render the specified React Email template with the `props` and `theme`.
    5. Dispatch the email using the configured email provider (e.g., Resend).
- **FR11: Refactor Email-Triggering Functions:** Any backend function that sends an email (e.g., `userRedemptions.redeem`) must be modified to call the new `emails:send` action via the scheduler: `ctx.scheduler.runAction(api.emails.send, { ... })`.

---

## 5. Non-Functional Requirements

| Category            | Requirement                                                                                                                                                                                                                                              |
| ------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Design & UX**     | Templates must be fully responsive and render correctly across all major email clients (Gmail, Outlook, Apple Mail). A tool like Litmus or Email on Acid should be used for testing.                                                                     |
| **Maintainability** | The separation of templates from branding logic should make the system easy to update. Adding a new email should not require any changes to the branding system itself. A README in `packages/emails` should document how to create a new branded email. |
| **Security**        | All client-provided data (logo URLs, footer text) must be treated as user input and handled securely. Footer text markdown must be parsed into safe HTML to prevent XSS.                                                                                 |
| **Performance**     | Fetching branding configuration should not add any noticeable latency to the email-sending process. Email rendering should be performant.                                                                                                                |
| **Accessibility**   | Emails must be WCAG 2.1 AA compliant. This includes sufficient color contrast (checked dynamically against provided brand colors), `alt` text for images/logos, and semantic HTML (`<h1>`, `<p>`, `<a>`).                                                |

---

## 6. Out of Scope for this PRD

- **A full WYSIWYG email editor for admins.** Admins will configure branding via simple input fields, not by designing templates.
- **User-level notification preferences.** This PRD focuses on client-level branding, not individual user settings.
- **A/B testing or analytics on emails.**
- **Dark Mode support.** The component architecture should accommodate this in the future, but it is not part of the initial implementation.

---

## 7. Success Metrics

| Metric                      | How We'll Measure It                                                                                                                                  | Target                                                           |
| --------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------- |
| **Developer Velocity**      | Time required for a developer to create and ship a new, fully-branded email type by following documentation.                                          | < 1 hour.                                                        |
| **Code Reusability**        | Amount of duplicated styling code across email templates.                                                                                             | 0 hardcoded colors or branding elements in any email template.   |
| **Client Adoption**         | Percentage of active clients who have configured their custom email branding. This will be tracked via a new metric on our admin analytics dashboard. | > 75% of clients adopt custom branding within 60 days of launch. |
| **Member & Staff Feedback** | Qualitative feedback from user surveys and client check-ins regarding email quality.                                                                  | Consistently positive feedback on the professionalism of emails. |

---

## 8. Open Questions / Assumptions

- **Question:** How do we handle brand colors that have poor contrast ratios (e.g., a client choosing yellow text on a white background)?
  - **Answer:** Provide a live warning in the admin UI but do not block saving. Empower the admin to make the final decision.
- **Question:** What is the fallback behavior for a broken logo URL?
  - **Answer:** The `<BrandedHeader>` component should gracefully omit the `<img>` tag if the URL is invalid or the image fails to load, preventing a broken image icon from appearing.
- **Question:** How will we handle the markdown in the `footerText`?
  - **Answer:** Use a library like `marked` to convert the markdown to HTML, and then sanitize it with a library like `dompurify` before rendering it in the email to prevent security vulnerabilities.
- **Assumption:** For V1, we will only refactor the two most common emails: `MemberRedemptionEmail` and `StaffNotificationEmail`. Other emails will be updated in subsequent phases.

---

## 9. Milestones & Timeline (High-Level Estimate)

- **Milestone 1: Backend & Schema (1 week):**
  - Update `clientConfiguration` schema and data migration.
  - Create `emails:send` action and supporting architecture in `packages/emails`.
  - Implement `defaultTheme` and `createTheme` utilities.
- **Milestone 2: Email Component Library (1 week):**
  - Build core reusable components in `packages/emails/src/components` (`BrandedHeader`, `Footer`, `BrandedButton`, etc.).
  - Refactor `MemberRedemptionEmail` to use the new components and `theme` prop.
- **Milestone 3: Admin UI (1.5 weeks):**
  - Build the `EmailBrandingSettings` form component.
  - Integrate the live preview pane.
  - Hook up the form to save branding data to the backend.
- **Milestone 4: Testing & Rollout (0.5 weeks):**
  - End-to-end testing.
  - Email client compatibility testing across major platforms.
  - Internal rollout and developer documentation.

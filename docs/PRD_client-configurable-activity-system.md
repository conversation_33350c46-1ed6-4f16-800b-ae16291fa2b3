# PRD: Client-Configurable Activity & Point System

**Document Status:** READY FOR DEVELOPMENT
**Author:** Gemini AI Architect
**Date:** 2024-07-26

---

## 1. Overview

This document outlines the requirements for the "Client-Configurable Activity & Point System" feature. The objective is to evolve the platform's current hardcoded "Class Attendance" activity into a fully dynamic system. This will empower Client Administrators to define custom activities, assign point values, and select unique icons, making the gamification engine more flexible and tailored to each client's business needs.

This PRD is a "living document" and will be updated as the feature progresses through the development lifecycle.

## 2. Product Goals

- **Empower Client Admins:** Provide clients with full, self-service control over their rewards economy and activity catalog.
- **Increase Platform Flexibility:** Transition from rigid, hardcoded business logic to a dynamic, multi-tenant configuration, increasing the platform's value proposition.
- **Enhance User Engagement:** Motivate deeper user interaction by offering a wider and more diverse set of point-earning opportunities.

## 3. Target Audience

- **Primary:** Client Administrator (e.g., <PERSON>ym/Studio Owner, HR Manager) who will use the Admin Dashboard to create and manage a rewards program that reflects their unique business offerings.
- **Secondary:** End User (e.g., Fitness Enthusiast) who will interact with the new system on their dashboard to log various activities and track their point earnings.

## 4. User Stories

- **ACT-1:** As a Client Admin, I want to perform CRUD operations (Create, View, Update, Delete) on the activities members can be rewarded for, so I can customize the platform to my business.
- **ACT-2:** As a Client Admin, I want to assign a specific point value to each activity, so I can control the rewards economy.
- **ACT-3:** As a Client Admin, I want to assign a unique icon to each activity to enhance branding and user experience.
- **ACT-4:** As an End User, I want to see a list of all available activities I can log, not just a single button, so I know all the ways I can earn points.
- **ACT-5:** As a developer, I want a single, generic backend mutation to log any activity for improved scalability and maintainability.

## 5. Functional & Technical Requirements

This section details the specific implementation plan, referencing existing codebase patterns and components.

### 5.1. Database Schema (`packages/db/convex/schema.ts`)

1.  **Modify `activityTypes` Table:** Add a `points` field to store the value of each activity.

    ```typescript
    // packages/db/convex/schema.ts

    // ... existing code ...
    activityTypes: defineTable({
      clientId: v.id('clients'),
      name: v.string(), // e.g., "Class Attendance"
      key: v.string(), // e.g., "class_attendance"
      icon: v.string(), // e.g., "activity" (a Huge Icon name)
      points: v.number(), // NEW FIELD
    }).index('by_client_id', ['clientId']),
    // ... existing code ...
    ```

2.  **Modify `activities` Table:** Add a `pointsAwarded` field to store the number of points a user received for a specific activity log. This is crucial for accurately displaying user history.

    ```typescript
    // packages/db/convex/schema.ts

    // ... existing code ...
    activities: defineTable({
      userId: v.id('users'),
      clientId: v.id('clients'),
      activityTypeKey: v.string(), // Changed from activityType
      timestamp: v.number(),
      pointsAwarded: v.number(), // NEW FIELD
      source: v.union(
        v.literal('manual'),
        v.literal('webhook'),
        v.literal('api_poll')
      ),
      externalReference: v.optional(
        v.object({
          provider: v.string(),
          externalId: v.string(),
          externalTimestamp: v.optional(v.number()),
        })
      ),
    });
    // ... existing code ...
    ```

### 5.2. Backend (`packages/db/convex/`)

1.  **Create `packages/db/convex/functions/activityTypes.ts`:**

    - This new file will house all CRUD operations for `activityTypes`.
    - All mutations must be `internalMutation` and use the existing `requireUserWithRole('admin')` helper from `../lib/auth.ts` to ensure only client admins can perform these actions.
    - **`create(args)`:** Creates a new `activityType`. Validates input using a Zod schema.
    - **`update(args)`:** Updates an existing `activityType`. Validates input.
    - **`delete(args)`:** Deletes an `activityType`. **Must adhere to the data integrity rule in section 6.3.**
    - **`getForClient()`:** An `internalQuery` that returns all `activityTypes` for the admin's client.

2.  **Refactor Activity Logging in `packages/db/convex/functions/activities.ts`:**
    - **Deprecate `logClassAttendance`:** This monolithic mutation should be removed.
    - **Create `logActivity(args: { activityTypeKey: string })`:** This new, generic `internalMutation` will replace the old one.
      - **Authentication:** Get the user and their `clientId` via `requireUser`.
      - **Lookup Activity Type:** Query the `activityTypes` table for the given `activityTypeKey` and the user's `clientId`. If not found, throw an error.
      - **Update User Points:** Directly patch the `users` table, adding the `points` from the looked-up activity type to the user's current points. `await ctx.db.patch(user._id, { points: user.points + activityType.points });`
      - **Log Activity:** Insert a new record into the `activities` table, filling in the new `pointsAwarded` field with the value from the `activityType`.
      - **Evaluate Milestones & Tiers:** Call the centralized `evaluateUserMilestones` and `evaluateAndUpdateUserTier` services to process progressions. This replaces the inline logic from the old function.
      - **Return Value:** Return a success status and any relevant information (e.g., points awarded, tier advancements).

### 5.3. Admin UI (`apps/web/`)

1.  **Update `apps/web/src/pages/AdminPage.tsx`:**

    - Add a new entry to the `TABS` constant for "Activities".
    - Use the `check-list` icon from `huge-icons`. For consistency, all icons in this component should be migrated from `lucide-react` to the project's standard `HugeIcon` component.

2.  **Create `apps/web/src/components/admin/ActivitiesManagementTab.tsx`:**

    - This component will be responsible for displaying the list of `activityTypes`.
    - It should reuse the existing layout and patterns from `RewardsManagementTab.tsx` and `MilestonesManagementTab.tsx`, including a header, a description, and an "Add Activity" button.
    - The main content will be a table displaying columns: `Icon`, `Name`, `Key`, `Points`, and `Actions` (Edit/Delete).

3.  **Create `apps/web/src/components/admin/ActivityTypeFormModal.tsx`:**
    - This modal will be used for both creating and editing `activityTypes`. It should be modeled directly after `MilestoneFormModal.tsx`.
    - **Fields:**
      - `Name` (e.g., "Personal Training Session")
      - `Key` (e.g., "personal_training"). Should be auto-generated from the name (kebab-case) but editable.
      - `Points` (e.g., 50). Must be a positive number.
      - `Icon Name`: A text input with a search/picker that queries the `huge-icons` library, similar to the `MilestoneFormModal`.
    - **Validation:** Use a Zod schema for client-side and server-side validation.

### 5.4. End-User UI (`apps/web/`)

1.  **Delete `apps/web/src/components/user/LogClassAttendanceButton.tsx`:** This file will no longer be needed.

2.  **Create `apps/web/src/components/user/LogActivityButton.tsx`:**

    - This new component will be the primary user interaction point for logging activities.
    - It will be a single button (e.g., "Log Activity") that, when clicked, opens a dropdown or modal.
    - The modal/dropdown will display a list of all available `activityTypes` for the client, fetched from `api.functions.activities.getActivityTypesForUser`. Each item in the list must show the activity's `icon` and `name`.
    - Clicking an activity from the list will trigger the `logActivity` mutation with the corresponding `activityType.key`.

3.  **Update `apps/web/src/pages/DashboardPage.tsx`:**
    - Replace the `<LogClassAttendanceButton />` with the new `<LogActivityButton />`.
    - The `activities` query in the "Recent Activities" section must be updated to reflect the new `pointsAwarded` field. The hardcoded "+10 pts" text must be replaced with the dynamic value from `activity.pointsAwarded`.

## 6. Non-Functional Requirements

- **Usability:** The admin interface must be intuitive, following the existing design patterns. The user-facing activity logger should be simple and fast to use.
- **Security:** All backend mutations must be secure against cross-tenant data access by strictly checking the user's `clientId` against the `clientId` of the resource being accessed. The `requireUserWithRole` helper is mandatory for all admin-level mutations.
- **Data Integrity:** An `activityType` is a dependency for `milestones`. Therefore, an admin **cannot delete** an `activityType` if it is currently being used by one or more milestones (i.e., its `key` matches a `milestone.conditions.activityTypeMatcher`). The `delete` mutation in `activityTypes.ts` must first query the `milestones` table to check for dependencies. If found, it must return an error, which the UI must then display gracefully to the admin (e.g., via a toast notification).
- **Migration & Seeding:** The `packages/db/convex/seed.ts` script (or equivalent data seeding script) must be updated. The default "Class Attendance" `activityType` must be given a default `points` value (e.g., 10) to ensure existing clients have a seamless transition.

## 7. Answered: Open Questions

- **What should the UI behavior be while the `logActivity` mutation is in progress?**
  - **Solution:** We will implement an optimistic UI pattern.
  - **Initiation:** When a user clicks an activity in the `LogActivityButton` modal/dropdown, that specific list item should become disabled and display a loading spinner to prevent duplicate clicks.
  - **Optimistic Update:** The user's total points displayed on the dashboard can be optimistically updated to reflect the new total immediately.
  - **Completion:** Upon successful completion of the `logActivity` mutation, Convex will re-fetch the relevant queries, and the UI will update with the correct server-side state. A success toast (e.g., "Activity logged! +25 points") should be displayed.
  - **Failure:** If the mutation fails, the optimistic update should be reverted, the loading state removed, and an error toast should be displayed to the user with a helpful message.

## 8. Success Metrics

- **Adoption:** 75% of active clients create at least one custom activity within 30 days of feature launch.
- **Engagement:** The average number of distinct activities logged per user per month increases from 1 to 1.8 within 60 days.
- **Usability:** 90% of client admins interact with the new "Activities" tab in the admin dashboard within 14 days of launch.

:root{color-scheme:light dark}body{--transparent-blue: #2196F355;--light-pink: #ff69b460;--gray: #888888;--sidebar-width: 250px;--box-shadow: rgba(0, 0, 0, .133) 0px 1.6px 3.6px 0px, rgba(0, 0, 0, .11) 0px .3px .9px 0px}html,body{width:100%;height:100%;padding:0;margin:0;overflow:hidden;display:flex;overscroll-behavior-x:none}#root{width:100%;height:100%;display:flex}body,dialog{background-color:var(--vscode-panel-background);color:var(--vscode-foreground);font-family:var(--vscode-font-family);font-weight:var(--vscode-font-weight);font-size:var(--vscode-font-size);-webkit-font-smoothing:antialiased}a{color:var(--vscode-textLink-foreground)}dialog{border:none;padding:0;box-shadow:var(--box-shadow);line-height:28px;max-width:400px}dialog .title{display:flex;align-items:center;margin:0;padding:0 5px;height:32px;background-color:var(--vscode-sideBar-background);max-width:400px}dialog .title .codicon{margin-right:3px}dialog .body{padding:10px;text-align:center}.button{color:var(--vscode-button-foreground);background:var(--vscode-button-background);margin:10px;border:none;height:28px;min-width:40px;cursor:pointer;-webkit-user-select:none;user-select:none}.button:focus{outline:1px solid var(--vscode-focusBorder)}.button:hover{background:var(--vscode-button-hoverBackground)}.button.secondary{color:var(--vscode-button-secondaryForeground);background:var(--vscode-button-secondaryBackground)}.button.secondary:hover{background:var(--vscode-button-secondaryHoverBackground)}*{box-sizing:border-box;min-width:0;min-height:0}*[hidden],.hidden{display:none!important}.invisible{visibility:hidden!important}svg{fill:currentColor}.vbox{display:flex;flex-direction:column;flex:auto;position:relative}.fill{position:absolute;top:0;right:0;bottom:0;left:0}.hbox{display:flex;flex:auto;position:relative}.spacer{flex:auto}.codicon-check{color:var(--vscode-charts-green)}.codicon-error{color:var(--vscode-errorForeground)}.codicon-warning{color:var(--vscode-list-warningForeground)}.codicon-circle-outline{color:var(--vscode-disabledForeground)}input[type=text],input[type=search]{color:var(--vscode-input-foreground);background-color:var(--vscode-input-background);border:none;outline:none}body.dark-mode ::-webkit-scrollbar{width:10px}body.dark-mode ::-webkit-scrollbar-thumb{background-color:#555}body.dark-mode ::-webkit-scrollbar-track{background-color:#333}body.dark-mode ::-webkit-scrollbar-thumb:hover{background-color:#777}body.dark-mode ::-webkit-scrollbar-track:hover{background-color:#444}.codicon-loading{animation:spin 1s infinite linear}::placeholder{color:var(--vscode-input-placeholderForeground)}@keyframes spin{to{transform:rotate(360deg)}}@font-face{font-family:codicon;src:url(./codicon.DCmgc-ay.ttf) format("truetype")}.codicon{font: 16px/1 codicon;flex:none;display:inline-block;text-decoration:none;text-rendering:auto;text-align:center;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.codicon-add:before{content:""}.codicon-plus:before{content:""}.codicon-gist-new:before{content:""}.codicon-repo-create:before{content:""}.codicon-lightbulb:before{content:""}.codicon-light-bulb:before{content:""}.codicon-repo:before{content:""}.codicon-repo-delete:before{content:""}.codicon-gist-fork:before{content:""}.codicon-repo-forked:before{content:""}.codicon-git-pull-request:before{content:""}.codicon-git-pull-request-abandoned:before{content:""}.codicon-record-keys:before{content:""}.codicon-keyboard:before{content:""}.codicon-tag:before{content:""}.codicon-git-pull-request-label:before{content:""}.codicon-tag-add:before{content:""}.codicon-tag-remove:before{content:""}.codicon-person:before{content:""}.codicon-person-follow:before{content:""}.codicon-person-outline:before{content:""}.codicon-person-filled:before{content:""}.codicon-git-branch:before{content:""}.codicon-git-branch-create:before{content:""}.codicon-git-branch-delete:before{content:""}.codicon-source-control:before{content:""}.codicon-mirror:before{content:""}.codicon-mirror-public:before{content:""}.codicon-star:before{content:""}.codicon-star-add:before{content:""}.codicon-star-delete:before{content:""}.codicon-star-empty:before{content:""}.codicon-comment:before{content:""}.codicon-comment-add:before{content:""}.codicon-alert:before{content:""}.codicon-warning:before{content:""}.codicon-search:before{content:""}.codicon-search-save:before{content:""}.codicon-log-out:before{content:""}.codicon-sign-out:before{content:""}.codicon-log-in:before{content:""}.codicon-sign-in:before{content:""}.codicon-eye:before{content:""}.codicon-eye-unwatch:before{content:""}.codicon-eye-watch:before{content:""}.codicon-circle-filled:before{content:""}.codicon-primitive-dot:before{content:""}.codicon-close-dirty:before{content:""}.codicon-debug-breakpoint:before{content:""}.codicon-debug-breakpoint-disabled:before{content:""}.codicon-debug-hint:before{content:""}.codicon-terminal-decoration-success:before{content:""}.codicon-primitive-square:before{content:""}.codicon-edit:before{content:""}.codicon-pencil:before{content:""}.codicon-info:before{content:""}.codicon-issue-opened:before{content:""}.codicon-gist-private:before{content:""}.codicon-git-fork-private:before{content:""}.codicon-lock:before{content:""}.codicon-mirror-private:before{content:""}.codicon-close:before{content:""}.codicon-remove-close:before{content:""}.codicon-x:before{content:""}.codicon-repo-sync:before{content:""}.codicon-sync:before{content:""}.codicon-clone:before{content:""}.codicon-desktop-download:before{content:""}.codicon-beaker:before{content:""}.codicon-microscope:before{content:""}.codicon-vm:before{content:""}.codicon-device-desktop:before{content:""}.codicon-file:before{content:""}.codicon-file-text:before{content:""}.codicon-more:before{content:""}.codicon-ellipsis:before{content:""}.codicon-kebab-horizontal:before{content:""}.codicon-mail-reply:before{content:""}.codicon-reply:before{content:""}.codicon-organization:before{content:""}.codicon-organization-filled:before{content:""}.codicon-organization-outline:before{content:""}.codicon-new-file:before{content:""}.codicon-file-add:before{content:""}.codicon-new-folder:before{content:""}.codicon-file-directory-create:before{content:""}.codicon-trash:before{content:""}.codicon-trashcan:before{content:""}.codicon-history:before{content:""}.codicon-clock:before{content:""}.codicon-folder:before{content:""}.codicon-file-directory:before{content:""}.codicon-symbol-folder:before{content:""}.codicon-logo-github:before{content:""}.codicon-mark-github:before{content:""}.codicon-github:before{content:""}.codicon-terminal:before{content:""}.codicon-console:before{content:""}.codicon-repl:before{content:""}.codicon-zap:before{content:""}.codicon-symbol-event:before{content:""}.codicon-error:before{content:""}.codicon-stop:before{content:""}.codicon-variable:before{content:""}.codicon-symbol-variable:before{content:""}.codicon-array:before{content:""}.codicon-symbol-array:before{content:""}.codicon-symbol-module:before{content:""}.codicon-symbol-package:before{content:""}.codicon-symbol-namespace:before{content:""}.codicon-symbol-object:before{content:""}.codicon-symbol-method:before{content:""}.codicon-symbol-function:before{content:""}.codicon-symbol-constructor:before{content:""}.codicon-symbol-boolean:before{content:""}.codicon-symbol-null:before{content:""}.codicon-symbol-numeric:before{content:""}.codicon-symbol-number:before{content:""}.codicon-symbol-structure:before{content:""}.codicon-symbol-struct:before{content:""}.codicon-symbol-parameter:before{content:""}.codicon-symbol-type-parameter:before{content:""}.codicon-symbol-key:before{content:""}.codicon-symbol-text:before{content:""}.codicon-symbol-reference:before{content:""}.codicon-go-to-file:before{content:""}.codicon-symbol-enum:before{content:""}.codicon-symbol-value:before{content:""}.codicon-symbol-ruler:before{content:""}.codicon-symbol-unit:before{content:""}.codicon-activate-breakpoints:before{content:""}.codicon-archive:before{content:""}.codicon-arrow-both:before{content:""}.codicon-arrow-down:before{content:""}.codicon-arrow-left:before{content:""}.codicon-arrow-right:before{content:""}.codicon-arrow-small-down:before{content:""}.codicon-arrow-small-left:before{content:""}.codicon-arrow-small-right:before{content:""}.codicon-arrow-small-up:before{content:""}.codicon-arrow-up:before{content:""}.codicon-bell:before{content:""}.codicon-bold:before{content:""}.codicon-book:before{content:""}.codicon-bookmark:before{content:""}.codicon-debug-breakpoint-conditional-unverified:before{content:""}.codicon-debug-breakpoint-conditional:before{content:""}.codicon-debug-breakpoint-conditional-disabled:before{content:""}.codicon-debug-breakpoint-data-unverified:before{content:""}.codicon-debug-breakpoint-data:before{content:""}.codicon-debug-breakpoint-data-disabled:before{content:""}.codicon-debug-breakpoint-log-unverified:before{content:""}.codicon-debug-breakpoint-log:before{content:""}.codicon-debug-breakpoint-log-disabled:before{content:""}.codicon-briefcase:before{content:""}.codicon-broadcast:before{content:""}.codicon-browser:before{content:""}.codicon-bug:before{content:""}.codicon-calendar:before{content:""}.codicon-case-sensitive:before{content:""}.codicon-check:before{content:""}.codicon-checklist:before{content:""}.codicon-chevron-down:before{content:""}.codicon-chevron-left:before{content:""}.codicon-chevron-right:before{content:""}.codicon-chevron-up:before{content:""}.codicon-chrome-close:before{content:""}.codicon-chrome-maximize:before{content:""}.codicon-chrome-minimize:before{content:""}.codicon-chrome-restore:before{content:""}.codicon-circle-outline:before{content:""}.codicon-circle:before{content:""}.codicon-debug-breakpoint-unverified:before{content:""}.codicon-terminal-decoration-incomplete:before{content:""}.codicon-circle-slash:before{content:""}.codicon-circuit-board:before{content:""}.codicon-clear-all:before{content:""}.codicon-clippy:before{content:""}.codicon-close-all:before{content:""}.codicon-cloud-download:before{content:""}.codicon-cloud-upload:before{content:""}.codicon-code:before{content:""}.codicon-collapse-all:before{content:""}.codicon-color-mode:before{content:""}.codicon-comment-discussion:before{content:""}.codicon-credit-card:before{content:""}.codicon-dash:before{content:""}.codicon-dashboard:before{content:""}.codicon-database:before{content:""}.codicon-debug-continue:before{content:""}.codicon-debug-disconnect:before{content:""}.codicon-debug-pause:before{content:""}.codicon-debug-restart:before{content:""}.codicon-debug-start:before{content:""}.codicon-debug-step-into:before{content:""}.codicon-debug-step-out:before{content:""}.codicon-debug-step-over:before{content:""}.codicon-debug-stop:before{content:""}.codicon-debug:before{content:""}.codicon-device-camera-video:before{content:""}.codicon-device-camera:before{content:""}.codicon-device-mobile:before{content:""}.codicon-diff-added:before{content:""}.codicon-diff-ignored:before{content:""}.codicon-diff-modified:before{content:""}.codicon-diff-removed:before{content:""}.codicon-diff-renamed:before{content:""}.codicon-diff:before{content:""}.codicon-diff-sidebyside:before{content:""}.codicon-discard:before{content:""}.codicon-editor-layout:before{content:""}.codicon-empty-window:before{content:""}.codicon-exclude:before{content:""}.codicon-extensions:before{content:""}.codicon-eye-closed:before{content:""}.codicon-file-binary:before{content:""}.codicon-file-code:before{content:""}.codicon-file-media:before{content:""}.codicon-file-pdf:before{content:""}.codicon-file-submodule:before{content:""}.codicon-file-symlink-directory:before{content:""}.codicon-file-symlink-file:before{content:""}.codicon-file-zip:before{content:""}.codicon-files:before{content:""}.codicon-filter:before{content:""}.codicon-flame:before{content:""}.codicon-fold-down:before{content:""}.codicon-fold-up:before{content:""}.codicon-fold:before{content:""}.codicon-folder-active:before{content:""}.codicon-folder-opened:before{content:""}.codicon-gear:before{content:""}.codicon-gift:before{content:""}.codicon-gist-secret:before{content:""}.codicon-gist:before{content:""}.codicon-git-commit:before{content:""}.codicon-git-compare:before{content:""}.codicon-compare-changes:before{content:""}.codicon-git-merge:before{content:""}.codicon-github-action:before{content:""}.codicon-github-alt:before{content:""}.codicon-globe:before{content:""}.codicon-grabber:before{content:""}.codicon-graph:before{content:""}.codicon-gripper:before{content:""}.codicon-heart:before{content:""}.codicon-home:before{content:""}.codicon-horizontal-rule:before{content:""}.codicon-hubot:before{content:""}.codicon-inbox:before{content:""}.codicon-issue-reopened:before{content:""}.codicon-issues:before{content:""}.codicon-italic:before{content:""}.codicon-jersey:before{content:""}.codicon-json:before{content:""}.codicon-kebab-vertical:before{content:""}.codicon-key:before{content:""}.codicon-law:before{content:""}.codicon-lightbulb-autofix:before{content:""}.codicon-link-external:before{content:""}.codicon-link:before{content:""}.codicon-list-ordered:before{content:""}.codicon-list-unordered:before{content:""}.codicon-live-share:before{content:""}.codicon-loading:before{content:""}.codicon-location:before{content:""}.codicon-mail-read:before{content:""}.codicon-mail:before{content:""}.codicon-markdown:before{content:""}.codicon-megaphone:before{content:""}.codicon-mention:before{content:""}.codicon-milestone:before{content:""}.codicon-git-pull-request-milestone:before{content:""}.codicon-mortar-board:before{content:""}.codicon-move:before{content:""}.codicon-multiple-windows:before{content:""}.codicon-mute:before{content:""}.codicon-no-newline:before{content:""}.codicon-note:before{content:""}.codicon-octoface:before{content:""}.codicon-open-preview:before{content:""}.codicon-package:before{content:""}.codicon-paintcan:before{content:""}.codicon-pin:before{content:""}.codicon-play:before{content:""}.codicon-run:before{content:""}.codicon-plug:before{content:""}.codicon-preserve-case:before{content:""}.codicon-preview:before{content:""}.codicon-project:before{content:""}.codicon-pulse:before{content:""}.codicon-question:before{content:""}.codicon-quote:before{content:""}.codicon-radio-tower:before{content:""}.codicon-reactions:before{content:""}.codicon-references:before{content:""}.codicon-refresh:before{content:""}.codicon-regex:before{content:""}.codicon-remote-explorer:before{content:""}.codicon-remote:before{content:""}.codicon-remove:before{content:""}.codicon-replace-all:before{content:""}.codicon-replace:before{content:""}.codicon-repo-clone:before{content:""}.codicon-repo-force-push:before{content:""}.codicon-repo-pull:before{content:""}.codicon-repo-push:before{content:""}.codicon-report:before{content:""}.codicon-request-changes:before{content:""}.codicon-rocket:before{content:""}.codicon-root-folder-opened:before{content:""}.codicon-root-folder:before{content:""}.codicon-rss:before{content:""}.codicon-ruby:before{content:""}.codicon-save-all:before{content:""}.codicon-save-as:before{content:""}.codicon-save:before{content:""}.codicon-screen-full:before{content:""}.codicon-screen-normal:before{content:""}.codicon-search-stop:before{content:""}.codicon-server:before{content:""}.codicon-settings-gear:before{content:""}.codicon-settings:before{content:""}.codicon-shield:before{content:""}.codicon-smiley:before{content:""}.codicon-sort-precedence:before{content:""}.codicon-split-horizontal:before{content:""}.codicon-split-vertical:before{content:""}.codicon-squirrel:before{content:""}.codicon-star-full:before{content:""}.codicon-star-half:before{content:""}.codicon-symbol-class:before{content:""}.codicon-symbol-color:before{content:""}.codicon-symbol-constant:before{content:""}.codicon-symbol-enum-member:before{content:""}.codicon-symbol-field:before{content:""}.codicon-symbol-file:before{content:""}.codicon-symbol-interface:before{content:""}.codicon-symbol-keyword:before{content:""}.codicon-symbol-misc:before{content:""}.codicon-symbol-operator:before{content:""}.codicon-symbol-property:before{content:""}.codicon-wrench:before{content:""}.codicon-wrench-subaction:before{content:""}.codicon-symbol-snippet:before{content:""}.codicon-tasklist:before{content:""}.codicon-telescope:before{content:""}.codicon-text-size:before{content:""}.codicon-three-bars:before{content:""}.codicon-thumbsdown:before{content:""}.codicon-thumbsup:before{content:""}.codicon-tools:before{content:""}.codicon-triangle-down:before{content:""}.codicon-triangle-left:before{content:""}.codicon-triangle-right:before{content:""}.codicon-triangle-up:before{content:""}.codicon-twitter:before{content:""}.codicon-unfold:before{content:""}.codicon-unlock:before{content:""}.codicon-unmute:before{content:""}.codicon-unverified:before{content:""}.codicon-verified:before{content:""}.codicon-versions:before{content:""}.codicon-vm-active:before{content:""}.codicon-vm-outline:before{content:""}.codicon-vm-running:before{content:""}.codicon-watch:before{content:""}.codicon-whitespace:before{content:""}.codicon-whole-word:before{content:""}.codicon-window:before{content:""}.codicon-word-wrap:before{content:""}.codicon-zoom-in:before{content:""}.codicon-zoom-out:before{content:""}.codicon-list-filter:before{content:""}.codicon-list-flat:before{content:""}.codicon-list-selection:before{content:""}.codicon-selection:before{content:""}.codicon-list-tree:before{content:""}.codicon-debug-breakpoint-function-unverified:before{content:""}.codicon-debug-breakpoint-function:before{content:""}.codicon-debug-breakpoint-function-disabled:before{content:""}.codicon-debug-stackframe-active:before{content:""}.codicon-circle-small-filled:before{content:""}.codicon-debug-stackframe-dot:before{content:""}.codicon-terminal-decoration-mark:before{content:""}.codicon-debug-stackframe:before{content:""}.codicon-debug-stackframe-focused:before{content:""}.codicon-debug-breakpoint-unsupported:before{content:""}.codicon-symbol-string:before{content:""}.codicon-debug-reverse-continue:before{content:""}.codicon-debug-step-back:before{content:""}.codicon-debug-restart-frame:before{content:""}.codicon-debug-alt:before{content:""}.codicon-call-incoming:before{content:""}.codicon-call-outgoing:before{content:""}.codicon-menu:before{content:""}.codicon-expand-all:before{content:""}.codicon-feedback:before{content:""}.codicon-git-pull-request-reviewer:before{content:""}.codicon-group-by-ref-type:before{content:""}.codicon-ungroup-by-ref-type:before{content:""}.codicon-account:before{content:""}.codicon-git-pull-request-assignee:before{content:""}.codicon-bell-dot:before{content:""}.codicon-debug-console:before{content:""}.codicon-library:before{content:""}.codicon-output:before{content:""}.codicon-run-all:before{content:""}.codicon-sync-ignored:before{content:""}.codicon-pinned:before{content:""}.codicon-github-inverted:before{content:""}.codicon-server-process:before{content:""}.codicon-server-environment:before{content:""}.codicon-pass:before{content:""}.codicon-issue-closed:before{content:""}.codicon-stop-circle:before{content:""}.codicon-play-circle:before{content:""}.codicon-record:before{content:""}.codicon-debug-alt-small:before{content:""}.codicon-vm-connect:before{content:""}.codicon-cloud:before{content:""}.codicon-merge:before{content:""}.codicon-export:before{content:""}.codicon-graph-left:before{content:""}.codicon-magnet:before{content:""}.codicon-notebook:before{content:""}.codicon-redo:before{content:""}.codicon-check-all:before{content:""}.codicon-pinned-dirty:before{content:""}.codicon-pass-filled:before{content:""}.codicon-circle-large-filled:before{content:""}.codicon-circle-large:before{content:""}.codicon-circle-large-outline:before{content:""}.codicon-combine:before{content:""}.codicon-gather:before{content:""}.codicon-table:before{content:""}.codicon-variable-group:before{content:""}.codicon-type-hierarchy:before{content:""}.codicon-type-hierarchy-sub:before{content:""}.codicon-type-hierarchy-super:before{content:""}.codicon-git-pull-request-create:before{content:""}.codicon-run-above:before{content:""}.codicon-run-below:before{content:""}.codicon-notebook-template:before{content:""}.codicon-debug-rerun:before{content:""}.codicon-workspace-trusted:before{content:""}.codicon-workspace-untrusted:before{content:""}.codicon-workspace-unknown:before{content:""}.codicon-terminal-cmd:before{content:""}.codicon-terminal-debian:before{content:""}.codicon-terminal-linux:before{content:""}.codicon-terminal-powershell:before{content:""}.codicon-terminal-tmux:before{content:""}.codicon-terminal-ubuntu:before{content:""}.codicon-terminal-bash:before{content:""}.codicon-arrow-swap:before{content:""}.codicon-copy:before{content:""}.codicon-person-add:before{content:""}.codicon-filter-filled:before{content:""}.codicon-wand:before{content:""}.codicon-debug-line-by-line:before{content:""}.codicon-inspect:before{content:""}.codicon-layers:before{content:""}.codicon-layers-dot:before{content:""}.codicon-layers-active:before{content:""}.codicon-compass:before{content:""}.codicon-compass-dot:before{content:""}.codicon-compass-active:before{content:""}.codicon-azure:before{content:""}.codicon-issue-draft:before{content:""}.codicon-git-pull-request-closed:before{content:""}.codicon-git-pull-request-draft:before{content:""}.codicon-debug-all:before{content:""}.codicon-debug-coverage:before{content:""}.codicon-run-errors:before{content:""}.codicon-folder-library:before{content:""}.codicon-debug-continue-small:before{content:""}.codicon-beaker-stop:before{content:""}.codicon-graph-line:before{content:""}.codicon-graph-scatter:before{content:""}.codicon-pie-chart:before{content:""}.codicon-bracket:before{content:""}.codicon-bracket-dot:before{content:""}.codicon-bracket-error:before{content:""}.codicon-lock-small:before{content:""}.codicon-azure-devops:before{content:""}.codicon-verified-filled:before{content:""}.codicon-newline:before{content:""}.codicon-layout:before{content:""}.codicon-layout-activitybar-left:before{content:""}.codicon-layout-activitybar-right:before{content:""}.codicon-layout-panel-left:before{content:""}.codicon-layout-panel-center:before{content:""}.codicon-layout-panel-justify:before{content:""}.codicon-layout-panel-right:before{content:""}.codicon-layout-panel:before{content:""}.codicon-layout-sidebar-left:before{content:""}.codicon-layout-sidebar-right:before{content:""}.codicon-layout-statusbar:before{content:""}.codicon-layout-menubar:before{content:""}.codicon-layout-centered:before{content:""}.codicon-target:before{content:""}.codicon-indent:before{content:""}.codicon-record-small:before{content:""}.codicon-error-small:before{content:""}.codicon-terminal-decoration-error:before{content:""}.codicon-arrow-circle-down:before{content:""}.codicon-arrow-circle-left:before{content:""}.codicon-arrow-circle-right:before{content:""}.codicon-arrow-circle-up:before{content:""}.codicon-layout-sidebar-right-off:before{content:""}.codicon-layout-panel-off:before{content:""}.codicon-layout-sidebar-left-off:before{content:""}.codicon-blank:before{content:""}.codicon-heart-filled:before{content:""}.codicon-map:before{content:""}.codicon-map-horizontal:before{content:""}.codicon-fold-horizontal:before{content:""}.codicon-map-filled:before{content:""}.codicon-map-horizontal-filled:before{content:""}.codicon-fold-horizontal-filled:before{content:""}.codicon-circle-small:before{content:""}.codicon-bell-slash:before{content:""}.codicon-bell-slash-dot:before{content:""}.codicon-comment-unresolved:before{content:""}.codicon-git-pull-request-go-to-changes:before{content:""}.codicon-git-pull-request-new-changes:before{content:""}.codicon-search-fuzzy:before{content:""}.codicon-comment-draft:before{content:""}.codicon-send:before{content:""}.codicon-sparkle:before{content:""}.codicon-insert:before{content:""}.codicon-mic:before{content:""}.codicon-thumbsdown-filled:before{content:""}.codicon-thumbsup-filled:before{content:""}.codicon-coffee:before{content:""}.codicon-snake:before{content:""}.codicon-game:before{content:""}.codicon-vr:before{content:""}.codicon-chip:before{content:""}.codicon-piano:before{content:""}.codicon-music:before{content:""}.codicon-mic-filled:before{content:""}.codicon-repo-fetch:before{content:""}.codicon-copilot:before{content:""}.codicon-lightbulb-sparkle:before{content:""}.codicon-robot:before{content:""}.codicon-sparkle-filled:before{content:""}.codicon-diff-single:before{content:""}.codicon-diff-multiple:before{content:""}.codicon-surround-with:before{content:""}.codicon-share:before{content:""}.codicon-git-stash:before{content:""}.codicon-git-stash-apply:before{content:""}.codicon-git-stash-pop:before{content:""}.codicon-vscode:before{content:""}.codicon-vscode-insiders:before{content:""}.codicon-code-oss:before{content:""}.codicon-run-coverage:before{content:""}.codicon-run-all-coverage:before{content:""}.codicon-coverage:before{content:""}.codicon-github-project:before{content:""}.codicon-map-vertical:before{content:""}.codicon-fold-vertical:before{content:""}.codicon-map-vertical-filled:before{content:""}.codicon-fold-vertical-filled:before{content:""}.codicon-go-to-search:before{content:""}.codicon-percentage:before{content:""}.codicon-sort-percentage:before{content:""}.codicon-attach:before{content:""}.codicon-git-fetch:before{content:""}.split-view{display:flex;flex:auto;position:relative}.split-view.vertical{flex-direction:column}.split-view.vertical.sidebar-first{flex-direction:column-reverse}.split-view.horizontal{flex-direction:row}.split-view.horizontal.sidebar-first{flex-direction:row-reverse}.split-view-main{display:flex;flex:auto}.split-view-sidebar{display:flex;flex:none}.split-view.vertical:not(.sidebar-first)>.split-view-sidebar{border-top:1px solid var(--vscode-panel-border)}.split-view.horizontal:not(.sidebar-first)>.split-view-sidebar{border-left:1px solid var(--vscode-panel-border)}.split-view.vertical.sidebar-first>.split-view-sidebar{border-bottom:1px solid var(--vscode-panel-border)}.split-view.horizontal.sidebar-first>.split-view-sidebar{border-right:1px solid var(--vscode-panel-border)}.split-view-resizer{position:absolute;z-index:100}.split-view.vertical>.split-view-resizer{left:0;right:0;height:12px;cursor:ns-resize}.split-view.horizontal>.split-view-resizer{top:0;bottom:0;width:12px;cursor:ew-resize}.action-title-line{display:block;overflow:hidden;text-overflow:ellipsis}.action-title-selector{text-overflow:ellipsis;overflow:hidden;color:var(--vscode-tab-inactiveForeground);margin-top:-8px}.action-title-method{white-space:pre;overflow:hidden;text-overflow:ellipsis}.action-title-param{color:var(--vscode-editorBracketHighlight-foreground1)}.action-location{display:flex;flex:none;margin:0 4px;color:var(--vscode-foreground)}.action-location>span{margin:0 4px;cursor:pointer;text-decoration:underline}.action-duration{display:flex;flex:none;align-items:center;margin:0 4px;color:var(--vscode-editorCodeLens-foreground)}.action-skipped{margin-right:4px}.action-icon{flex:none;display:flex;align-items:center;padding-right:3px}.action-icons{flex:none;display:flex;flex-direction:row;cursor:pointer;height:20px;position:relative;top:1px;border-bottom:1px solid transparent}.action-icons:hover{border-bottom:1px solid var(--vscode-sideBarTitle-foreground)}.action-error{color:var(--vscode-errorForeground);position:relative;margin-right:2px;flex:none}.action-parameter{display:inline;flex:none;padding-left:5px}.action-locator-parameter{color:var(--vscode-charts-orange)}.action-generic-parameter{color:var(--vscode-charts-purple)}.action-url{display:inline;flex:none;padding-left:5px;color:var(--vscode-charts-blue)}.action-list-show-all{display:flex;cursor:pointer;height:28px;align-items:center}.tree-view-content{display:flex;flex-direction:column;flex:auto;position:relative;-webkit-user-select:none;user-select:none;overflow:hidden auto;outline:1px solid transparent}.tree-view-entry{display:flex;flex:none;cursor:pointer;align-items:center;white-space:nowrap;line-height:28px;padding-left:5px}.tree-view-content.not-selectable>.tree-view-entry{cursor:inherit}.tree-view-entry.highlighted:not(.selected){background-color:var(--vscode-list-inactiveSelectionBackground)!important}.tree-view-entry.selected{z-index:10}.tree-view-indent{min-width:16px}.tree-view-content:focus .tree-view-entry.selected{background-color:var(--vscode-list-activeSelectionBackground);color:var(--vscode-list-activeSelectionForeground);outline:1px solid var(--vscode-focusBorder)}.tree-view-content .tree-view-entry.selected{background-color:var(--vscode-list-inactiveSelectionBackground)}.tree-view-content:focus .tree-view-entry.selected *{color:var(--vscode-list-activeSelectionForeground)!important;background-color:transparent!important}.tree-view-content:focus .tree-view-entry.selected .codicon{color:var(--vscode-list-activeSelectionForeground)!important}.tree-view-content:focus .tree-view-entry.selected button.eye.toggled{border-radius:6px;outline:1px solid var(--vscode-button-foreground)}.tree-view-empty{flex:auto;display:flex;align-items:center;justify-content:center}.tree-view-entry.error{color:var(--vscode-list-errorForeground);background-color:var(--vscode-inputValidation-errorBackground)}.tree-view-entry.warning{color:var(--vscode-list-warningForeground);background-color:var(--vscode-inputValidation-warningBackground)}.tree-view-entry.info{background-color:var(--vscode-inputValidation-infoBackground)}.toolbar-button{flex:none;border:none;outline:none;color:var(--vscode-sideBarTitle-foreground);background:transparent;padding:4px;cursor:pointer;display:inline-flex;align-items:center}.toolbar-button:disabled{color:var(--vscode-disabledForeground)!important;cursor:default}.toolbar-button:not(:disabled):hover{background-color:var(--vscode-toolbar-hoverBackground)}.toolbar-button:not(:disabled):active{background-color:var(--vscode-toolbar-activeBackground)}.toolbar-button.toggled{color:var(--vscode-notificationLink-foreground)}.toolbar-separator{flex:none;background-color:var(--vscode-menu-separatorBackground);width:1px;padding:0;margin:5px 4px;height:16px}.call-tab{flex:auto;line-height:24px;white-space:pre;overflow:auto;-webkit-user-select:text;user-select:text}.call-error{border-bottom:1px solid var(--vscode-panel-border);padding:3px 0 3px 12px}.call-error .codicon{color:var(--vscode-errorForeground);position:relative;top:2px;margin-right:2px}.call-section{padding-left:6px;padding-top:2px;margin-top:2px;font-weight:700;text-transform:uppercase;font-size:10px;color:var(--vscode-sideBarTitle-foreground);line-height:24px}.call-section:not(:first-child){border-top:1px solid var(--vscode-panel-border)}.call-line{padding:4px 0 4px 6px;display:flex;align-items:center;text-overflow:ellipsis;overflow:hidden;line-height:20px;white-space:nowrap}.call-line:not(:hover) .toolbar-button.copy{display:none}.call-line .toolbar-button.copy{margin-left:5px;margin-top:-2px;margin-bottom:-2px}.call-value{margin-left:2px;text-overflow:ellipsis;overflow:hidden}a.call-value{text-decoration:none}a.call-value:hover{text-decoration:underline}.call-value:before{content:" "}.call-value.datetime,.call-value.string,.call-value.locator{color:var(--vscode-charts-orange)}.call-value.number,.call-value.bigint,.call-value.boolean,.call-value.symbol,.call-value.undefined,.call-value.function,.call-value.object{color:var(--vscode-charts-blue)}.call-tab .error-message{padding:5px;line-height:17px}.copy-to-clipboard-text-button{background-color:var(--vscode-editor-inactiveSelectionBackground);border:none;padding:4px 12px;cursor:pointer}.list-view-content{display:flex;flex-direction:column;flex:auto;position:relative;-webkit-user-select:none;user-select:none;overflow:hidden auto;outline:1px solid transparent}.list-view-entry{display:flex;flex:none;cursor:pointer;align-items:center;white-space:nowrap;line-height:28px;padding-left:5px}.list-view-content.not-selectable>.list-view-entry{cursor:inherit}.list-view-entry.highlighted:not(.selected){background-color:var(--vscode-list-inactiveSelectionBackground)!important}.list-view-entry.selected{z-index:10}.list-view-indent{min-width:16px}.list-view-content:focus .list-view-entry.selected{background-color:var(--vscode-list-activeSelectionBackground);color:var(--vscode-list-activeSelectionForeground);outline:1px solid var(--vscode-focusBorder)}.list-view-content .list-view-entry.selected{background-color:var(--vscode-list-inactiveSelectionBackground)}.list-view-content:focus .list-view-entry.selected *{color:var(--vscode-list-activeSelectionForeground)!important;background-color:transparent!important}.list-view-content:focus .list-view-entry.selected .codicon{color:var(--vscode-list-activeSelectionForeground)!important}.list-view-empty{flex:auto;display:flex;align-items:center;justify-content:center}.list-view-entry.error{color:var(--vscode-list-errorForeground);background-color:var(--vscode-inputValidation-errorBackground)}.list-view-entry.warning{color:var(--vscode-list-warningForeground);background-color:var(--vscode-inputValidation-warningBackground)}.list-view-entry.info{background-color:var(--vscode-inputValidation-infoBackground)}.log-list-duration{display:flex;flex:none;align-items:center;color:var(--vscode-editorCodeLens-foreground);float:right;margin-right:5px;-webkit-user-select:none;user-select:none}.log-list-item{text-wrap:wrap;-webkit-user-select:text;user-select:text;width:100%}.error-message{font-family:var(--vscode-editor-font-family);font-weight:var(--vscode-editor-font-weight);font-size:var(--vscode-editor-font-size);white-space:pre-wrap;word-break:break-word;padding:10px}.attachments-tab{flex:auto;line-height:24px;white-space:pre;overflow:auto;-webkit-user-select:text;user-select:text}.attachments-section{padding-left:6px;font-weight:700;text-transform:uppercase;font-size:12px;color:var(--vscode-sideBarTitle-foreground);line-height:24px}.attachments-section:not(:first-child){border-top:1px solid var(--vscode-panel-border);margin-top:10px}.attachment-item{margin:4px 8px}.attachment-title-highlight{text-decoration:underline var(--vscode-terminal-findMatchBackground);text-decoration-thickness:1.5px}.attachment-item img{flex:none;min-width:200px;max-width:80%;box-shadow:0 12px 28px #0003,0 2px 4px #0000001a}a.codicon-cloud-download:hover{background-color:var(--vscode-list-inactiveSelectionBackground)}.yellow-flash{animation:yellowflash-bg 2s}@keyframes yellowflash-bg{0%{background:var(--vscode-peekViewEditor-matchHighlightBackground)}to{background:transparent}}body{--vscode-font-family: system-ui, "Ubuntu", "Droid Sans", sans-serif;--vscode-font-weight: normal;--vscode-font-size: 13px;--vscode-editor-font-family: "Droid Sans Mono", "monospace", monospace;--vscode-editor-font-weight: normal;--vscode-editor-font-size: 14px;--vscode-foreground: #616161;--vscode-disabledForeground: rgba(97, 97, 97, .5);--vscode-errorForeground: #a1260d;--vscode-descriptionForeground: #717171;--vscode-icon-foreground: #424242;--vscode-focusBorder: #0090f1;--vscode-textSeparator-foreground: rgba(0, 0, 0, .18);--vscode-textLink-foreground: #006ab1;--vscode-textLink-activeForeground: #006ab1;--vscode-textPreformat-foreground: #a31515;--vscode-textBlockQuote-background: rgba(127, 127, 127, .1);--vscode-textBlockQuote-border: rgba(0, 122, 204, .5);--vscode-textCodeBlock-background: rgba(220, 220, 220, .4);--vscode-widget-shadow: rgba(0, 0, 0, .16);--vscode-input-background: #ffffff;--vscode-input-foreground: #616161;--vscode-inputOption-activeBorder: #007acc;--vscode-inputOption-hoverBackground: rgba(184, 184, 184, .31);--vscode-inputOption-activeBackground: rgba(0, 144, 241, .2);--vscode-inputOption-activeForeground: #000000;--vscode-input-placeholderForeground: #767676;--vscode-inputValidation-infoBackground: #d6ecf2;--vscode-inputValidation-infoBorder: #007acc;--vscode-inputValidation-warningBackground: #f6f5d2;--vscode-inputValidation-warningBorder: #b89500;--vscode-inputValidation-errorBackground: #f2dede;--vscode-inputValidation-errorBorder: #be1100;--vscode-dropdown-background: #ffffff;--vscode-dropdown-border: #cecece;--vscode-checkbox-background: #ffffff;--vscode-checkbox-border: #cecece;--vscode-button-foreground: #ffffff;--vscode-button-separator: rgba(255, 255, 255, .4);--vscode-button-background: #007acc;--vscode-button-hoverBackground: #0062a3;--vscode-button-secondaryForeground: #ffffff;--vscode-button-secondaryBackground: #5f6a79;--vscode-button-secondaryHoverBackground: #4c5561;--vscode-badge-background: #c4c4c4;--vscode-badge-foreground: #333333;--vscode-scrollbar-shadow: #dddddd;--vscode-scrollbarSlider-background: rgba(100, 100, 100, .4);--vscode-scrollbarSlider-hoverBackground: rgba(100, 100, 100, .7);--vscode-scrollbarSlider-activeBackground: rgba(0, 0, 0, .6);--vscode-progressBar-background: #0e70c0;--vscode-editorError-foreground: #e51400;--vscode-editorWarning-foreground: #bf8803;--vscode-editorInfo-foreground: #1a85ff;--vscode-editorHint-foreground: #6c6c6c;--vscode-sash-hoverBorder: #0090f1;--vscode-editor-background: #ffffff;--vscode-editor-foreground: #000000;--vscode-editorStickyScroll-background: #ffffff;--vscode-editorStickyScrollHover-background: #f0f0f0;--vscode-editorWidget-background: #f3f3f3;--vscode-editorWidget-foreground: #616161;--vscode-editorWidget-border: #c8c8c8;--vscode-quickInput-background: #f3f3f3;--vscode-quickInput-foreground: #616161;--vscode-quickInputTitle-background: rgba(0, 0, 0, .06);--vscode-pickerGroup-foreground: #0066bf;--vscode-pickerGroup-border: #cccedb;--vscode-keybindingLabel-background: rgba(221, 221, 221, .4);--vscode-keybindingLabel-foreground: #555555;--vscode-keybindingLabel-border: rgba(204, 204, 204, .4);--vscode-keybindingLabel-bottomBorder: rgba(187, 187, 187, .4);--vscode-editor-selectionBackground: #add6ff;--vscode-editor-inactiveSelectionBackground: #e5ebf1;--vscode-editor-selectionHighlightBackground: rgba(173, 214, 255, .5);--vscode-editor-findMatchBackground: #a8ac94;--vscode-editor-findMatchHighlightBackground: rgba(234, 92, 0, .33);--vscode-editor-findRangeHighlightBackground: rgba(180, 180, 180, .3);--vscode-searchEditor-findMatchBackground: rgba(234, 92, 0, .22);--vscode-editor-hoverHighlightBackground: rgba(173, 214, 255, .15);--vscode-editorHoverWidget-background: #f3f3f3;--vscode-editorHoverWidget-foreground: #616161;--vscode-editorHoverWidget-border: #c8c8c8;--vscode-editorHoverWidget-statusBarBackground: #e7e7e7;--vscode-editorLink-activeForeground: #0000ff;--vscode-editorInlayHint-foreground: rgba(51, 51, 51, .8);--vscode-editorInlayHint-background: rgba(196, 196, 196, .3);--vscode-editorInlayHint-typeForeground: rgba(51, 51, 51, .8);--vscode-editorInlayHint-typeBackground: rgba(196, 196, 196, .3);--vscode-editorInlayHint-parameterForeground: rgba(51, 51, 51, .8);--vscode-editorInlayHint-parameterBackground: rgba(196, 196, 196, .3);--vscode-editorLightBulb-foreground: #ddb100;--vscode-editorLightBulbAutoFix-foreground: #007acc;--vscode-diffEditor-insertedTextBackground: rgba(156, 204, 44, .4);--vscode-diffEditor-removedTextBackground: rgba(255, 0, 0, .3);--vscode-diffEditor-insertedLineBackground: rgba(155, 185, 85, .2);--vscode-diffEditor-removedLineBackground: rgba(255, 0, 0, .2);--vscode-diffEditor-diagonalFill: rgba(34, 34, 34, .2);--vscode-list-focusOutline: #0090f1;--vscode-list-focusAndSelectionOutline: #90c2f9;--vscode-list-activeSelectionBackground: #0060c0;--vscode-list-activeSelectionForeground: #ffffff;--vscode-list-activeSelectionIconForeground: #ffffff;--vscode-list-inactiveSelectionBackground: #e4e6f1;--vscode-list-hoverBackground: #e8e8e8;--vscode-list-dropBackground: #d6ebff;--vscode-list-highlightForeground: #0066bf;--vscode-list-focusHighlightForeground: #bbe7ff;--vscode-list-invalidItemForeground: #b89500;--vscode-list-errorForeground: #b01011;--vscode-list-warningForeground: #855f00;--vscode-listFilterWidget-background: #f3f3f3;--vscode-listFilterWidget-outline: rgba(0, 0, 0, 0);--vscode-listFilterWidget-noMatchesOutline: #be1100;--vscode-listFilterWidget-shadow: rgba(0, 0, 0, .16);--vscode-list-filterMatchBackground: rgba(234, 92, 0, .33);--vscode-tree-indentGuidesStroke: #a9a9a9;--vscode-tree-tableColumnsBorder: rgba(97, 97, 97, .13);--vscode-tree-tableOddRowsBackground: rgba(97, 97, 97, .04);--vscode-list-deemphasizedForeground: #8e8e90;--vscode-quickInputList-focusForeground: #ffffff;--vscode-quickInputList-focusIconForeground: #ffffff;--vscode-quickInputList-focusBackground: #0060c0;--vscode-menu-foreground: #616161;--vscode-menu-background: #ffffff;--vscode-menu-selectionForeground: #ffffff;--vscode-menu-selectionBackground: #0060c0;--vscode-menu-separatorBackground: #d4d4d4;--vscode-toolbar-hoverBackground: rgba(184, 184, 184, .31);--vscode-toolbar-activeBackground: rgba(166, 166, 166, .31);--vscode-editor-snippetTabstopHighlightBackground: rgba(10, 50, 100, .2);--vscode-editor-snippetFinalTabstopHighlightBorder: rgba(10, 50, 100, .5);--vscode-breadcrumb-foreground: rgba(97, 97, 97, .8);--vscode-breadcrumb-background: #ffffff;--vscode-breadcrumb-focusForeground: #4e4e4e;--vscode-breadcrumb-activeSelectionForeground: #4e4e4e;--vscode-breadcrumbPicker-background: #f3f3f3;--vscode-merge-currentHeaderBackground: rgba(64, 200, 174, .5);--vscode-merge-currentContentBackground: rgba(64, 200, 174, .2);--vscode-merge-incomingHeaderBackground: rgba(64, 166, 255, .5);--vscode-merge-incomingContentBackground: rgba(64, 166, 255, .2);--vscode-merge-commonHeaderBackground: rgba(96, 96, 96, .4);--vscode-merge-commonContentBackground: rgba(96, 96, 96, .16);--vscode-editorOverviewRuler-currentContentForeground: rgba(64, 200, 174, .5);--vscode-editorOverviewRuler-incomingContentForeground: rgba(64, 166, 255, .5);--vscode-editorOverviewRuler-commonContentForeground: rgba(96, 96, 96, .4);--vscode-editorOverviewRuler-findMatchForeground: rgba(209, 134, 22, .49);--vscode-editorOverviewRuler-selectionHighlightForeground: rgba(160, 160, 160, .8);--vscode-minimap-findMatchHighlight: #d18616;--vscode-minimap-selectionOccurrenceHighlight: #c9c9c9;--vscode-minimap-selectionHighlight: #add6ff;--vscode-minimap-errorHighlight: rgba(255, 18, 18, .7);--vscode-minimap-warningHighlight: #bf8803;--vscode-minimap-foregroundOpacity: #000000;--vscode-minimapSlider-background: rgba(100, 100, 100, .2);--vscode-minimapSlider-hoverBackground: rgba(100, 100, 100, .35);--vscode-minimapSlider-activeBackground: rgba(0, 0, 0, .3);--vscode-problemsErrorIcon-foreground: #e51400;--vscode-problemsWarningIcon-foreground: #bf8803;--vscode-problemsInfoIcon-foreground: #1a85ff;--vscode-charts-foreground: #616161;--vscode-charts-lines: rgba(97, 97, 97, .5);--vscode-charts-red: #e51400;--vscode-charts-blue: #1a85ff;--vscode-charts-yellow: #bf8803;--vscode-charts-orange: #d18616;--vscode-charts-green: #388a34;--vscode-charts-purple: #652d90;--vscode-editor-lineHighlightBorder: #eeeeee;--vscode-editor-rangeHighlightBackground: rgba(253, 255, 0, .2);--vscode-editor-symbolHighlightBackground: rgba(234, 92, 0, .33);--vscode-editorCursor-foreground: #000000;--vscode-editorWhitespace-foreground: rgba(51, 51, 51, .2);--vscode-editorIndentGuide-background: #d3d3d3;--vscode-editorIndentGuide-activeBackground: #939393;--vscode-editorLineNumber-foreground: #237893;--vscode-editorActiveLineNumber-foreground: #0b216f;--vscode-editorLineNumber-activeForeground: #0b216f;--vscode-editorRuler-foreground: #d3d3d3;--vscode-editorCodeLens-foreground: #919191;--vscode-editorBracketMatch-background: rgba(0, 100, 0, .1);--vscode-editorBracketMatch-border: #b9b9b9;--vscode-editorOverviewRuler-border: rgba(127, 127, 127, .3);--vscode-editorGutter-background: #ffffff;--vscode-editorUnnecessaryCode-opacity: rgba(0, 0, 0, .47);--vscode-editorGhostText-foreground: rgba(0, 0, 0, .47);--vscode-editorOverviewRuler-rangeHighlightForeground: rgba(0, 122, 204, .6);--vscode-editorOverviewRuler-errorForeground: rgba(255, 18, 18, .7);--vscode-editorOverviewRuler-warningForeground: #bf8803;--vscode-editorOverviewRuler-infoForeground: #1a85ff;--vscode-editorBracketHighlight-foreground1: #0431fa;--vscode-editorBracketHighlight-foreground2: #319331;--vscode-editorBracketHighlight-foreground3: #7b3814;--vscode-editorBracketHighlight-foreground4: rgba(0, 0, 0, 0);--vscode-editorBracketHighlight-foreground5: rgba(0, 0, 0, 0);--vscode-editorBracketHighlight-foreground6: rgba(0, 0, 0, 0);--vscode-editorBracketHighlight-unexpectedBracket\.foreground: rgba(255, 18, 18, .8);--vscode-editorBracketPairGuide-background1: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background2: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background3: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background4: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background5: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background6: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground1: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground2: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground3: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground4: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground5: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground6: rgba(0, 0, 0, 0);--vscode-editorUnicodeHighlight-border: #cea33d;--vscode-editorUnicodeHighlight-background: rgba(206, 163, 61, .08);--vscode-symbolIcon-arrayForeground: #616161;--vscode-symbolIcon-booleanForeground: #616161;--vscode-symbolIcon-classForeground: #d67e00;--vscode-symbolIcon-colorForeground: #616161;--vscode-symbolIcon-constantForeground: #616161;--vscode-symbolIcon-constructorForeground: #652d90;--vscode-symbolIcon-enumeratorForeground: #d67e00;--vscode-symbolIcon-enumeratorMemberForeground: #007acc;--vscode-symbolIcon-eventForeground: #d67e00;--vscode-symbolIcon-fieldForeground: #007acc;--vscode-symbolIcon-fileForeground: #616161;--vscode-symbolIcon-folderForeground: #616161;--vscode-symbolIcon-functionForeground: #652d90;--vscode-symbolIcon-interfaceForeground: #007acc;--vscode-symbolIcon-keyForeground: #616161;--vscode-symbolIcon-keywordForeground: #616161;--vscode-symbolIcon-methodForeground: #652d90;--vscode-symbolIcon-moduleForeground: #616161;--vscode-symbolIcon-namespaceForeground: #616161;--vscode-symbolIcon-nullForeground: #616161;--vscode-symbolIcon-numberForeground: #616161;--vscode-symbolIcon-objectForeground: #616161;--vscode-symbolIcon-operatorForeground: #616161;--vscode-symbolIcon-packageForeground: #616161;--vscode-symbolIcon-propertyForeground: #616161;--vscode-symbolIcon-referenceForeground: #616161;--vscode-symbolIcon-snippetForeground: #616161;--vscode-symbolIcon-stringForeground: #616161;--vscode-symbolIcon-structForeground: #616161;--vscode-symbolIcon-textForeground: #616161;--vscode-symbolIcon-typeParameterForeground: #616161;--vscode-symbolIcon-unitForeground: #616161;--vscode-symbolIcon-variableForeground: #007acc;--vscode-editorHoverWidget-highlightForeground: #0066bf;--vscode-editorOverviewRuler-bracketMatchForeground: #a0a0a0;--vscode-editor-foldBackground: rgba(173, 214, 255, .3);--vscode-editorGutter-foldingControlForeground: #424242;--vscode-editor-linkedEditingBackground: rgba(255, 0, 0, .3);--vscode-editor-wordHighlightBackground: rgba(87, 87, 87, .25);--vscode-editor-wordHighlightStrongBackground: rgba(14, 99, 156, .25);--vscode-editorOverviewRuler-wordHighlightForeground: rgba(160, 160, 160, .8);--vscode-editorOverviewRuler-wordHighlightStrongForeground: rgba(192, 160, 192, .8);--vscode-peekViewTitle-background: rgba(26, 133, 255, .1);--vscode-peekViewTitleLabel-foreground: #000000;--vscode-peekViewTitleDescription-foreground: #616161;--vscode-peekView-border: #1a85ff;--vscode-peekViewResult-background: #f3f3f3;--vscode-peekViewResult-lineForeground: #646465;--vscode-peekViewResult-fileForeground: #1e1e1e;--vscode-peekViewResult-selectionBackground: rgba(51, 153, 255, .2);--vscode-peekViewResult-selectionForeground: #6c6c6c;--vscode-peekViewEditor-background: #f2f8fc;--vscode-peekViewEditorGutter-background: #f2f8fc;--vscode-peekViewResult-matchHighlightBackground: rgba(234, 92, 0, .3);--vscode-peekViewEditor-matchHighlightBackground: rgba(245, 216, 2, .87);--vscode-editorMarkerNavigationError-background: #e51400;--vscode-editorMarkerNavigationError-headerBackground: rgba(229, 20, 0, .1);--vscode-editorMarkerNavigationWarning-background: #bf8803;--vscode-editorMarkerNavigationWarning-headerBackground: rgba(191, 136, 3, .1);--vscode-editorMarkerNavigationInfo-background: #1a85ff;--vscode-editorMarkerNavigationInfo-headerBackground: rgba(26, 133, 255, .1);--vscode-editorMarkerNavigation-background: #ffffff;--vscode-editorSuggestWidget-background: #f3f3f3;--vscode-editorSuggestWidget-border: #c8c8c8;--vscode-editorSuggestWidget-foreground: #000000;--vscode-editorSuggestWidget-selectedForeground: #ffffff;--vscode-editorSuggestWidget-selectedIconForeground: #ffffff;--vscode-editorSuggestWidget-selectedBackground: #0060c0;--vscode-editorSuggestWidget-highlightForeground: #0066bf;--vscode-editorSuggestWidget-focusHighlightForeground: #bbe7ff;--vscode-editorSuggestWidgetStatus-foreground: rgba(0, 0, 0, .5);--vscode-tab-activeBackground: #ffffff;--vscode-tab-unfocusedActiveBackground: #ffffff;--vscode-tab-inactiveBackground: #ececec;--vscode-tab-unfocusedInactiveBackground: #ececec;--vscode-tab-activeForeground: #333333;--vscode-tab-inactiveForeground: rgba(51, 51, 51, .7);--vscode-tab-unfocusedActiveForeground: rgba(51, 51, 51, .7);--vscode-tab-unfocusedInactiveForeground: rgba(51, 51, 51, .35);--vscode-tab-border: #f3f3f3;--vscode-tab-lastPinnedBorder: rgba(97, 97, 97, .19);--vscode-tab-activeModifiedBorder: #33aaee;--vscode-tab-inactiveModifiedBorder: rgba(51, 170, 238, .5);--vscode-tab-unfocusedActiveModifiedBorder: rgba(51, 170, 238, .7);--vscode-tab-unfocusedInactiveModifiedBorder: rgba(51, 170, 238, .25);--vscode-editorPane-background: #ffffff;--vscode-editorGroupHeader-tabsBackground: #f3f3f3;--vscode-editorGroupHeader-noTabsBackground: #ffffff;--vscode-editorGroup-border: #e7e7e7;--vscode-editorGroup-dropBackground: rgba(38, 119, 203, .18);--vscode-editorGroup-dropIntoPromptForeground: #616161;--vscode-editorGroup-dropIntoPromptBackground: #f3f3f3;--vscode-sideBySideEditor-horizontalBorder: #e7e7e7;--vscode-sideBySideEditor-verticalBorder: #e7e7e7;--vscode-panel-background: #ffffff;--vscode-panel-border: rgba(128, 128, 128, .35);--vscode-panelTitle-activeForeground: #424242;--vscode-panelTitle-inactiveForeground: rgba(66, 66, 66, .75);--vscode-panelTitle-activeBorder: #424242;--vscode-panelInput-border: #dddddd;--vscode-panel-dropBorder: #424242;--vscode-panelSection-dropBackground: rgba(38, 119, 203, .18);--vscode-panelSectionHeader-background: rgba(128, 128, 128, .2);--vscode-panelSection-border: rgba(128, 128, 128, .35);--vscode-banner-background: #004386;--vscode-banner-foreground: #ffffff;--vscode-banner-iconForeground: #1a85ff;--vscode-statusBar-foreground: #ffffff;--vscode-statusBar-noFolderForeground: #ffffff;--vscode-statusBar-background: #007acc;--vscode-statusBar-noFolderBackground: #68217a;--vscode-statusBar-focusBorder: #ffffff;--vscode-statusBarItem-activeBackground: rgba(255, 255, 255, .18);--vscode-statusBarItem-focusBorder: #ffffff;--vscode-statusBarItem-hoverBackground: rgba(255, 255, 255, .12);--vscode-statusBarItem-compactHoverBackground: rgba(255, 255, 255, .2);--vscode-statusBarItem-prominentForeground: #ffffff;--vscode-statusBarItem-prominentBackground: rgba(0, 0, 0, .5);--vscode-statusBarItem-prominentHoverBackground: rgba(0, 0, 0, .3);--vscode-statusBarItem-errorBackground: #c72e0f;--vscode-statusBarItem-errorForeground: #ffffff;--vscode-statusBarItem-warningBackground: #725102;--vscode-statusBarItem-warningForeground: #ffffff;--vscode-activityBar-background: #2c2c2c;--vscode-activityBar-foreground: #ffffff;--vscode-activityBar-inactiveForeground: rgba(255, 255, 255, .4);--vscode-activityBar-activeBorder: #ffffff;--vscode-activityBar-dropBorder: #ffffff;--vscode-activityBarBadge-background: #007acc;--vscode-activityBarBadge-foreground: #ffffff;--vscode-statusBarItem-remoteBackground: #16825d;--vscode-statusBarItem-remoteForeground: #ffffff;--vscode-extensionBadge-remoteBackground: #007acc;--vscode-extensionBadge-remoteForeground: #ffffff;--vscode-sideBar-background: #f3f3f3;--vscode-sideBarTitle-foreground: #6f6f6f;--vscode-sideBar-dropBackground: rgba(38, 119, 203, .18);--vscode-sideBarSectionHeader-background: rgba(0, 0, 0, 0);--vscode-sideBarSectionHeader-border: rgba(97, 97, 97, .19);--vscode-titleBar-activeForeground: #333333;--vscode-titleBar-inactiveForeground: rgba(51, 51, 51, .6);--vscode-titleBar-activeBackground: #dddddd;--vscode-titleBar-inactiveBackground: rgba(221, 221, 221, .6);--vscode-menubar-selectionForeground: #333333;--vscode-menubar-selectionBackground: rgba(184, 184, 184, .31);--vscode-notifications-foreground: #616161;--vscode-notifications-background: #f3f3f3;--vscode-notificationLink-foreground: #006ab1;--vscode-notificationCenterHeader-background: #e7e7e7;--vscode-notifications-border: #e7e7e7;--vscode-notificationsErrorIcon-foreground: #e51400;--vscode-notificationsWarningIcon-foreground: #bf8803;--vscode-notificationsInfoIcon-foreground: #1a85ff;--vscode-commandCenter-foreground: #333333;--vscode-commandCenter-activeForeground: #333333;--vscode-commandCenter-activeBackground: rgba(184, 184, 184, .31);--vscode-commandCenter-border: rgba(128, 128, 128, .35);--vscode-editorCommentsWidget-resolvedBorder: rgba(97, 97, 97, .5);--vscode-editorCommentsWidget-unresolvedBorder: #1a85ff;--vscode-editorCommentsWidget-rangeBackground: rgba(26, 133, 255, .1);--vscode-editorCommentsWidget-rangeBorder: rgba(26, 133, 255, .4);--vscode-editorCommentsWidget-rangeActiveBackground: rgba(26, 133, 255, .1);--vscode-editorCommentsWidget-rangeActiveBorder: rgba(26, 133, 255, .4);--vscode-editorGutter-commentRangeForeground: #d5d8e9;--vscode-debugToolBar-background: #f3f3f3;--vscode-debugIcon-startForeground: #388a34;--vscode-editor-stackFrameHighlightBackground: rgba(255, 255, 102, .45);--vscode-editor-focusedStackFrameHighlightBackground: rgba(206, 231, 206, .45);--vscode-mergeEditor-change\.background: rgba(155, 185, 85, .2);--vscode-mergeEditor-change\.word\.background: rgba(156, 204, 44, .4);--vscode-mergeEditor-conflict\.unhandledUnfocused\.border: rgba(255, 166, 0, .48);--vscode-mergeEditor-conflict\.unhandledFocused\.border: #ffa600;--vscode-mergeEditor-conflict\.handledUnfocused\.border: rgba(134, 134, 134, .29);--vscode-mergeEditor-conflict\.handledFocused\.border: rgba(193, 193, 193, .8);--vscode-mergeEditor-conflict\.handled\.minimapOverViewRuler: rgba(173, 172, 168, .93);--vscode-mergeEditor-conflict\.unhandled\.minimapOverViewRuler: #fcba03;--vscode-mergeEditor-conflictingLines\.background: rgba(255, 234, 0, .28);--vscode-settings-headerForeground: #444444;--vscode-settings-modifiedItemIndicator: #66afe0;--vscode-settings-headerBorder: rgba(128, 128, 128, .35);--vscode-settings-sashBorder: rgba(128, 128, 128, .35);--vscode-settings-dropdownBackground: #ffffff;--vscode-settings-dropdownBorder: #cecece;--vscode-settings-dropdownListBorder: #c8c8c8;--vscode-settings-checkboxBackground: #ffffff;--vscode-settings-checkboxBorder: #cecece;--vscode-settings-textInputBackground: #ffffff;--vscode-settings-textInputForeground: #616161;--vscode-settings-textInputBorder: #cecece;--vscode-settings-numberInputBackground: #ffffff;--vscode-settings-numberInputForeground: #616161;--vscode-settings-numberInputBorder: #cecece;--vscode-settings-focusedRowBackground: rgba(232, 232, 232, .6);--vscode-settings-rowHoverBackground: rgba(232, 232, 232, .3);--vscode-settings-focusedRowBorder: rgba(0, 0, 0, .12);--vscode-terminal-foreground: #333333;--vscode-terminal-selectionBackground: #add6ff;--vscode-terminal-inactiveSelectionBackground: #e5ebf1;--vscode-terminalCommandDecoration-defaultBackground: rgba(0, 0, 0, .25);--vscode-terminalCommandDecoration-successBackground: #2090d3;--vscode-terminalCommandDecoration-errorBackground: #e51400;--vscode-terminalOverviewRuler-cursorForeground: rgba(160, 160, 160, .8);--vscode-terminal-border: rgba(128, 128, 128, .35);--vscode-terminal-findMatchBackground: #a8ac94;--vscode-terminal-findMatchHighlightBackground: rgba(234, 92, 0, .33);--vscode-terminalOverviewRuler-findMatchForeground: rgba(209, 134, 22, .49);--vscode-terminal-dropBackground: rgba(38, 119, 203, .18);--vscode-testing-iconFailed: #f14c4c;--vscode-testing-iconErrored: #f14c4c;--vscode-testing-iconPassed: #73c991;--vscode-testing-runAction: #73c991;--vscode-testing-iconQueued: #cca700;--vscode-testing-iconUnset: #848484;--vscode-testing-iconSkipped: #848484;--vscode-testing-peekBorder: #e51400;--vscode-testing-peekHeaderBackground: rgba(229, 20, 0, .1);--vscode-testing-message\.error\.decorationForeground: #e51400;--vscode-testing-message\.error\.lineBackground: rgba(255, 0, 0, .2);--vscode-testing-message\.info\.decorationForeground: rgba(0, 0, 0, .5);--vscode-welcomePage-tileBackground: #f3f3f3;--vscode-welcomePage-tileHoverBackground: #dbdbdb;--vscode-welcomePage-tileShadow: rgba(0, 0, 0, .16);--vscode-welcomePage-progress\.background: #ffffff;--vscode-welcomePage-progress\.foreground: #006ab1;--vscode-debugExceptionWidget-border: #a31515;--vscode-debugExceptionWidget-background: #f1dfde;--vscode-ports-iconRunningProcessForeground: #369432;--vscode-statusBar-debuggingBackground: #cc6633;--vscode-statusBar-debuggingForeground: #ffffff;--vscode-editor-inlineValuesForeground: rgba(0, 0, 0, .5);--vscode-editor-inlineValuesBackground: rgba(255, 200, 0, .2);--vscode-editorGutter-modifiedBackground: #2090d3;--vscode-editorGutter-addedBackground: #48985d;--vscode-editorGutter-deletedBackground: #e51400;--vscode-minimapGutter-modifiedBackground: #2090d3;--vscode-minimapGutter-addedBackground: #48985d;--vscode-minimapGutter-deletedBackground: #e51400;--vscode-editorOverviewRuler-modifiedForeground: rgba(32, 144, 211, .6);--vscode-editorOverviewRuler-addedForeground: rgba(72, 152, 93, .6);--vscode-editorOverviewRuler-deletedForeground: rgba(229, 20, 0, .6);--vscode-debugIcon-breakpointForeground: #e51400;--vscode-debugIcon-breakpointDisabledForeground: #848484;--vscode-debugIcon-breakpointUnverifiedForeground: #848484;--vscode-debugIcon-breakpointCurrentStackframeForeground: #be8700;--vscode-debugIcon-breakpointStackframeForeground: #89d185;--vscode-notebook-cellBorderColor: #e8e8e8;--vscode-notebook-focusedEditorBorder: #0090f1;--vscode-notebookStatusSuccessIcon-foreground: #388a34;--vscode-notebookStatusErrorIcon-foreground: #a1260d;--vscode-notebookStatusRunningIcon-foreground: #616161;--vscode-notebook-cellToolbarSeparator: rgba(128, 128, 128, .35);--vscode-notebook-selectedCellBackground: rgba(200, 221, 241, .31);--vscode-notebook-selectedCellBorder: #e8e8e8;--vscode-notebook-focusedCellBorder: #0090f1;--vscode-notebook-inactiveFocusedCellBorder: #e8e8e8;--vscode-notebook-cellStatusBarItemHoverBackground: rgba(0, 0, 0, .08);--vscode-notebook-cellInsertionIndicator: #0090f1;--vscode-notebookScrollbarSlider-background: rgba(100, 100, 100, .4);--vscode-notebookScrollbarSlider-hoverBackground: rgba(100, 100, 100, .7);--vscode-notebookScrollbarSlider-activeBackground: rgba(0, 0, 0, .6);--vscode-notebook-symbolHighlightBackground: rgba(253, 255, 0, .2);--vscode-notebook-cellEditorBackground: #f3f3f3;--vscode-notebook-editorBackground: #ffffff;--vscode-keybindingTable-headerBackground: rgba(97, 97, 97, .04);--vscode-keybindingTable-rowsBackground: rgba(97, 97, 97, .04);--vscode-scm-providerBorder: #c8c8c8;--vscode-searchEditor-textInputBorder: #cecece;--vscode-debugTokenExpression-name: #9b46b0;--vscode-debugTokenExpression-value: rgba(108, 108, 108, .8);--vscode-debugTokenExpression-string: #a31515;--vscode-debugTokenExpression-boolean: #0000ff;--vscode-debugTokenExpression-number: #098658;--vscode-debugTokenExpression-error: #e51400;--vscode-debugView-exceptionLabelForeground: #ffffff;--vscode-debugView-exceptionLabelBackground: #a31515;--vscode-debugView-stateLabelForeground: #616161;--vscode-debugView-stateLabelBackground: rgba(136, 136, 136, .27);--vscode-debugView-valueChangedHighlight: #569cd6;--vscode-debugConsole-infoForeground: #1a85ff;--vscode-debugConsole-warningForeground: #bf8803;--vscode-debugConsole-errorForeground: #a1260d;--vscode-debugConsole-sourceForeground: #616161;--vscode-debugConsoleInputIcon-foreground: #616161;--vscode-debugIcon-pauseForeground: #007acc;--vscode-debugIcon-stopForeground: #a1260d;--vscode-debugIcon-disconnectForeground: #a1260d;--vscode-debugIcon-restartForeground: #388a34;--vscode-debugIcon-stepOverForeground: #007acc;--vscode-debugIcon-stepIntoForeground: #007acc;--vscode-debugIcon-stepOutForeground: #007acc;--vscode-debugIcon-continueForeground: #007acc;--vscode-debugIcon-stepBackForeground: #007acc;--vscode-extensionButton-prominentBackground: #007acc;--vscode-extensionButton-prominentForeground: #ffffff;--vscode-extensionButton-prominentHoverBackground: #0062a3;--vscode-extensionIcon-starForeground: #df6100;--vscode-extensionIcon-verifiedForeground: #006ab1;--vscode-extensionIcon-preReleaseForeground: #1d9271;--vscode-extensionIcon-sponsorForeground: #b51e78;--vscode-terminal-ansiBlack: #000000;--vscode-terminal-ansiRed: #cd3131;--vscode-terminal-ansiGreen: #00bc00;--vscode-terminal-ansiYellow: #949800;--vscode-terminal-ansiBlue: #0451a5;--vscode-terminal-ansiMagenta: #bc05bc;--vscode-terminal-ansiCyan: #0598bc;--vscode-terminal-ansiWhite: #555555;--vscode-terminal-ansiBrightBlack: #666666;--vscode-terminal-ansiBrightRed: #cd3131;--vscode-terminal-ansiBrightGreen: #14ce14;--vscode-terminal-ansiBrightYellow: #b5ba00;--vscode-terminal-ansiBrightBlue: #0451a5;--vscode-terminal-ansiBrightMagenta: #bc05bc;--vscode-terminal-ansiBrightCyan: #0598bc;--vscode-terminal-ansiBrightWhite: #a5a5a5;--vscode-interactive-activeCodeBorder: #1a85ff;--vscode-interactive-inactiveCodeBorder: #e4e6f1;--vscode-gitDecoration-addedResourceForeground: #587c0c;--vscode-gitDecoration-modifiedResourceForeground: #895503;--vscode-gitDecoration-deletedResourceForeground: #ad0707;--vscode-gitDecoration-renamedResourceForeground: #007100;--vscode-gitDecoration-untrackedResourceForeground: #007100;--vscode-gitDecoration-ignoredResourceForeground: #8e8e90;--vscode-gitDecoration-stageModifiedResourceForeground: #895503;--vscode-gitDecoration-stageDeletedResourceForeground: #ad0707;--vscode-gitDecoration-conflictingResourceForeground: #ad0707;--vscode-gitDecoration-submoduleResourceForeground: #1258a7}body.dark-mode{--vscode-font-family: system-ui, "Ubuntu", "Droid Sans", sans-serif;--vscode-font-weight: normal;--vscode-font-size: 13px;--vscode-editor-font-family: "Droid Sans Mono", "monospace", monospace;--vscode-editor-font-weight: normal;--vscode-editor-font-size: 14px;--vscode-foreground: #cccccc;--vscode-disabledForeground: rgba(204, 204, 204, .5);--vscode-errorForeground: #f48771;--vscode-descriptionForeground: rgba(204, 204, 204, .7);--vscode-icon-foreground: #c5c5c5;--vscode-focusBorder: #007fd4;--vscode-textSeparator-foreground: rgba(255, 255, 255, .18);--vscode-textLink-foreground: #3794ff;--vscode-textLink-activeForeground: #3794ff;--vscode-textPreformat-foreground: #d7ba7d;--vscode-textBlockQuote-background: rgba(127, 127, 127, .1);--vscode-textBlockQuote-border: rgba(0, 122, 204, .5);--vscode-textCodeBlock-background: rgba(10, 10, 10, .4);--vscode-widget-shadow: rgba(0, 0, 0, .36);--vscode-input-background: #3c3c3c;--vscode-input-foreground: #cccccc;--vscode-inputOption-activeBorder: #007acc;--vscode-inputOption-hoverBackground: rgba(90, 93, 94, .5);--vscode-inputOption-activeBackground: rgba(0, 127, 212, .4);--vscode-inputOption-activeForeground: #ffffff;--vscode-input-placeholderForeground: #a6a6a6;--vscode-inputValidation-infoBackground: #063b49;--vscode-inputValidation-infoBorder: #007acc;--vscode-inputValidation-warningBackground: #352a05;--vscode-inputValidation-warningBorder: #b89500;--vscode-inputValidation-errorBackground: #5a1d1d;--vscode-inputValidation-errorBorder: #be1100;--vscode-dropdown-background: #3c3c3c;--vscode-dropdown-foreground: #f0f0f0;--vscode-dropdown-border: #3c3c3c;--vscode-checkbox-background: #3c3c3c;--vscode-checkbox-foreground: #f0f0f0;--vscode-checkbox-border: #3c3c3c;--vscode-button-foreground: #ffffff;--vscode-button-separator: rgba(255, 255, 255, .4);--vscode-button-background: #0e639c;--vscode-button-hoverBackground: #1177bb;--vscode-button-secondaryForeground: #ffffff;--vscode-button-secondaryBackground: #3a3d41;--vscode-button-secondaryHoverBackground: #45494e;--vscode-badge-background: #4d4d4d;--vscode-badge-foreground: #ffffff;--vscode-scrollbar-shadow: #000000;--vscode-scrollbarSlider-background: rgba(121, 121, 121, .4);--vscode-scrollbarSlider-hoverBackground: rgba(100, 100, 100, .7);--vscode-scrollbarSlider-activeBackground: rgba(191, 191, 191, .4);--vscode-progressBar-background: #0e70c0;--vscode-editorError-foreground: #f14c4c;--vscode-editorWarning-foreground: #cca700;--vscode-editorInfo-foreground: #3794ff;--vscode-editorHint-foreground: rgba(238, 238, 238, .7);--vscode-sash-hoverBorder: #007fd4;--vscode-editor-background: #1e1e1e;--vscode-editor-foreground: #d4d4d4;--vscode-editorStickyScroll-background: #1e1e1e;--vscode-editorStickyScrollHover-background: #2a2d2e;--vscode-editorWidget-background: #252526;--vscode-editorWidget-foreground: #cccccc;--vscode-editorWidget-border: #454545;--vscode-quickInput-background: #252526;--vscode-quickInput-foreground: #cccccc;--vscode-quickInputTitle-background: rgba(255, 255, 255, .1);--vscode-pickerGroup-foreground: #3794ff;--vscode-pickerGroup-border: #3f3f46;--vscode-keybindingLabel-background: rgba(128, 128, 128, .17);--vscode-keybindingLabel-foreground: #cccccc;--vscode-keybindingLabel-border: rgba(51, 51, 51, .6);--vscode-keybindingLabel-bottomBorder: rgba(68, 68, 68, .6);--vscode-editor-selectionBackground: #264f78;--vscode-editor-inactiveSelectionBackground: #3a3d41;--vscode-editor-selectionHighlightBackground: rgba(173, 214, 255, .15);--vscode-editor-findMatchBackground: #515c6a;--vscode-editor-findMatchHighlightBackground: rgba(234, 92, 0, .33);--vscode-editor-findRangeHighlightBackground: rgba(58, 61, 65, .4);--vscode-searchEditor-findMatchBackground: rgba(234, 92, 0, .22);--vscode-editor-hoverHighlightBackground: rgba(38, 79, 120, .25);--vscode-editorHoverWidget-background: #252526;--vscode-editorHoverWidget-foreground: #cccccc;--vscode-editorHoverWidget-border: #454545;--vscode-editorHoverWidget-statusBarBackground: #2c2c2d;--vscode-editorLink-activeForeground: #4e94ce;--vscode-editorInlayHint-foreground: rgba(255, 255, 255, .8);--vscode-editorInlayHint-background: rgba(77, 77, 77, .6);--vscode-editorInlayHint-typeForeground: rgba(255, 255, 255, .8);--vscode-editorInlayHint-typeBackground: rgba(77, 77, 77, .6);--vscode-editorInlayHint-parameterForeground: rgba(255, 255, 255, .8);--vscode-editorInlayHint-parameterBackground: rgba(77, 77, 77, .6);--vscode-editorLightBulb-foreground: #ffcc00;--vscode-editorLightBulbAutoFix-foreground: #75beff;--vscode-diffEditor-insertedTextBackground: rgba(156, 204, 44, .2);--vscode-diffEditor-removedTextBackground: rgba(255, 0, 0, .4);--vscode-diffEditor-insertedLineBackground: rgba(155, 185, 85, .2);--vscode-diffEditor-removedLineBackground: rgba(255, 0, 0, .2);--vscode-diffEditor-diagonalFill: rgba(204, 204, 204, .2);--vscode-list-focusOutline: #007fd4;--vscode-list-activeSelectionBackground: #04395e;--vscode-list-activeSelectionForeground: #ffffff;--vscode-list-activeSelectionIconForeground: #ffffff;--vscode-list-inactiveSelectionBackground: #37373d;--vscode-list-hoverBackground: #2a2d2e;--vscode-list-dropBackground: #383b3d;--vscode-list-highlightForeground: #2aaaff;--vscode-list-focusHighlightForeground: #2aaaff;--vscode-list-invalidItemForeground: #b89500;--vscode-list-errorForeground: #f88070;--vscode-list-warningForeground: #cca700;--vscode-listFilterWidget-background: #252526;--vscode-listFilterWidget-outline: rgba(0, 0, 0, 0);--vscode-listFilterWidget-noMatchesOutline: #be1100;--vscode-listFilterWidget-shadow: rgba(0, 0, 0, .36);--vscode-list-filterMatchBackground: rgba(234, 92, 0, .33);--vscode-tree-indentGuidesStroke: #585858;--vscode-tree-tableColumnsBorder: rgba(204, 204, 204, .13);--vscode-tree-tableOddRowsBackground: rgba(204, 204, 204, .04);--vscode-list-deemphasizedForeground: #8c8c8c;--vscode-quickInputList-focusForeground: #ffffff;--vscode-quickInputList-focusIconForeground: #ffffff;--vscode-quickInputList-focusBackground: #04395e;--vscode-menu-foreground: #cccccc;--vscode-menu-background: #303031;--vscode-menu-selectionForeground: #ffffff;--vscode-menu-selectionBackground: #04395e;--vscode-menu-separatorBackground: #606060;--vscode-toolbar-hoverBackground: rgba(90, 93, 94, .31);--vscode-toolbar-activeBackground: rgba(99, 102, 103, .31);--vscode-editor-snippetTabstopHighlightBackground: rgba(124, 124, 124, .3);--vscode-editor-snippetFinalTabstopHighlightBorder: #525252;--vscode-breadcrumb-foreground: rgba(204, 204, 204, .8);--vscode-breadcrumb-background: #1e1e1e;--vscode-breadcrumb-focusForeground: #e0e0e0;--vscode-breadcrumb-activeSelectionForeground: #e0e0e0;--vscode-breadcrumbPicker-background: #252526;--vscode-merge-currentHeaderBackground: rgba(64, 200, 174, .5);--vscode-merge-currentContentBackground: rgba(64, 200, 174, .2);--vscode-merge-incomingHeaderBackground: rgba(64, 166, 255, .5);--vscode-merge-incomingContentBackground: rgba(64, 166, 255, .2);--vscode-merge-commonHeaderBackground: rgba(96, 96, 96, .4);--vscode-merge-commonContentBackground: rgba(96, 96, 96, .16);--vscode-editorOverviewRuler-currentContentForeground: rgba(64, 200, 174, .5);--vscode-editorOverviewRuler-incomingContentForeground: rgba(64, 166, 255, .5);--vscode-editorOverviewRuler-commonContentForeground: rgba(96, 96, 96, .4);--vscode-editorOverviewRuler-findMatchForeground: rgba(209, 134, 22, .49);--vscode-editorOverviewRuler-selectionHighlightForeground: rgba(160, 160, 160, .8);--vscode-minimap-findMatchHighlight: #d18616;--vscode-minimap-selectionOccurrenceHighlight: #676767;--vscode-minimap-selectionHighlight: #264f78;--vscode-minimap-errorHighlight: rgba(255, 18, 18, .7);--vscode-minimap-warningHighlight: #cca700;--vscode-minimap-foregroundOpacity: #000000;--vscode-minimapSlider-background: rgba(121, 121, 121, .2);--vscode-minimapSlider-hoverBackground: rgba(100, 100, 100, .35);--vscode-minimapSlider-activeBackground: rgba(191, 191, 191, .2);--vscode-problemsErrorIcon-foreground: #f14c4c;--vscode-problemsWarningIcon-foreground: #cca700;--vscode-problemsInfoIcon-foreground: #3794ff;--vscode-charts-foreground: #cccccc;--vscode-charts-lines: rgba(204, 204, 204, .5);--vscode-charts-red: #f14c4c;--vscode-charts-blue: #3794ff;--vscode-charts-yellow: #cca700;--vscode-charts-orange: #d18616;--vscode-charts-green: #89d185;--vscode-charts-purple: #b180d7;--vscode-editor-lineHighlightBorder: #282828;--vscode-editor-rangeHighlightBackground: rgba(255, 255, 255, .04);--vscode-editor-symbolHighlightBackground: rgba(234, 92, 0, .33);--vscode-editorCursor-foreground: #aeafad;--vscode-editorWhitespace-foreground: rgba(227, 228, 226, .16);--vscode-editorIndentGuide-background: #404040;--vscode-editorIndentGuide-activeBackground: #707070;--vscode-editorLineNumber-foreground: #858585;--vscode-editorActiveLineNumber-foreground: #c6c6c6;--vscode-editorLineNumber-activeForeground: #c6c6c6;--vscode-editorRuler-foreground: #5a5a5a;--vscode-editorCodeLens-foreground: #999999;--vscode-editorBracketMatch-background: rgba(0, 100, 0, .1);--vscode-editorBracketMatch-border: #888888;--vscode-editorOverviewRuler-border: rgba(127, 127, 127, .3);--vscode-editorGutter-background: #1e1e1e;--vscode-editorUnnecessaryCode-opacity: rgba(0, 0, 0, .67);--vscode-editorGhostText-foreground: rgba(255, 255, 255, .34);--vscode-editorOverviewRuler-rangeHighlightForeground: rgba(0, 122, 204, .6);--vscode-editorOverviewRuler-errorForeground: rgba(255, 18, 18, .7);--vscode-editorOverviewRuler-warningForeground: #cca700;--vscode-editorOverviewRuler-infoForeground: #3794ff;--vscode-editorBracketHighlight-foreground1: #ffd700;--vscode-editorBracketHighlight-foreground2: #da70d6;--vscode-editorBracketHighlight-foreground3: #179fff;--vscode-editorBracketHighlight-foreground4: rgba(0, 0, 0, 0);--vscode-editorBracketHighlight-foreground5: rgba(0, 0, 0, 0);--vscode-editorBracketHighlight-foreground6: rgba(0, 0, 0, 0);--vscode-editorBracketHighlight-unexpectedBracket\.foreground: rgba(255, 18, 18, .8);--vscode-editorBracketPairGuide-background1: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background2: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background3: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background4: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background5: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-background6: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground1: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground2: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground3: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground4: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground5: rgba(0, 0, 0, 0);--vscode-editorBracketPairGuide-activeBackground6: rgba(0, 0, 0, 0);--vscode-editorUnicodeHighlight-border: #bd9b03;--vscode-editorUnicodeHighlight-background: rgba(189, 155, 3, .15);--vscode-symbolIcon-arrayForeground: #cccccc;--vscode-symbolIcon-booleanForeground: #cccccc;--vscode-symbolIcon-classForeground: #ee9d28;--vscode-symbolIcon-colorForeground: #cccccc;--vscode-symbolIcon-constantForeground: #cccccc;--vscode-symbolIcon-constructorForeground: #b180d7;--vscode-symbolIcon-enumeratorForeground: #ee9d28;--vscode-symbolIcon-enumeratorMemberForeground: #75beff;--vscode-symbolIcon-eventForeground: #ee9d28;--vscode-symbolIcon-fieldForeground: #75beff;--vscode-symbolIcon-fileForeground: #cccccc;--vscode-symbolIcon-folderForeground: #cccccc;--vscode-symbolIcon-functionForeground: #b180d7;--vscode-symbolIcon-interfaceForeground: #75beff;--vscode-symbolIcon-keyForeground: #cccccc;--vscode-symbolIcon-keywordForeground: #cccccc;--vscode-symbolIcon-methodForeground: #b180d7;--vscode-symbolIcon-moduleForeground: #cccccc;--vscode-symbolIcon-namespaceForeground: #cccccc;--vscode-symbolIcon-nullForeground: #cccccc;--vscode-symbolIcon-numberForeground: #cccccc;--vscode-symbolIcon-objectForeground: #cccccc;--vscode-symbolIcon-operatorForeground: #cccccc;--vscode-symbolIcon-packageForeground: #cccccc;--vscode-symbolIcon-propertyForeground: #cccccc;--vscode-symbolIcon-referenceForeground: #cccccc;--vscode-symbolIcon-snippetForeground: #cccccc;--vscode-symbolIcon-stringForeground: #cccccc;--vscode-symbolIcon-structForeground: #cccccc;--vscode-symbolIcon-textForeground: #cccccc;--vscode-symbolIcon-typeParameterForeground: #cccccc;--vscode-symbolIcon-unitForeground: #cccccc;--vscode-symbolIcon-variableForeground: #75beff;--vscode-editorHoverWidget-highlightForeground: #2aaaff;--vscode-editorOverviewRuler-bracketMatchForeground: #a0a0a0;--vscode-editor-foldBackground: rgba(38, 79, 120, .3);--vscode-editorGutter-foldingControlForeground: #c5c5c5;--vscode-editor-linkedEditingBackground: rgba(255, 0, 0, .3);--vscode-editor-wordHighlightBackground: rgba(87, 87, 87, .72);--vscode-editor-wordHighlightStrongBackground: rgba(0, 73, 114, .72);--vscode-editorOverviewRuler-wordHighlightForeground: rgba(160, 160, 160, .8);--vscode-editorOverviewRuler-wordHighlightStrongForeground: rgba(192, 160, 192, .8);--vscode-peekViewTitle-background: rgba(55, 148, 255, .1);--vscode-peekViewTitleLabel-foreground: #ffffff;--vscode-peekViewTitleDescription-foreground: rgba(204, 204, 204, .7);--vscode-peekView-border: #3794ff;--vscode-peekViewResult-background: #252526;--vscode-peekViewResult-lineForeground: #bbbbbb;--vscode-peekViewResult-fileForeground: #ffffff;--vscode-peekViewResult-selectionBackground: rgba(51, 153, 255, .2);--vscode-peekViewResult-selectionForeground: #ffffff;--vscode-peekViewEditor-background: #001f33;--vscode-peekViewEditorGutter-background: #001f33;--vscode-peekViewResult-matchHighlightBackground: rgba(234, 92, 0, .3);--vscode-peekViewEditor-matchHighlightBackground: rgba(255, 143, 0, .6);--vscode-editorMarkerNavigationError-background: #f14c4c;--vscode-editorMarkerNavigationError-headerBackground: rgba(241, 76, 76, .1);--vscode-editorMarkerNavigationWarning-background: #cca700;--vscode-editorMarkerNavigationWarning-headerBackground: rgba(204, 167, 0, .1);--vscode-editorMarkerNavigationInfo-background: #3794ff;--vscode-editorMarkerNavigationInfo-headerBackground: rgba(55, 148, 255, .1);--vscode-editorMarkerNavigation-background: #1e1e1e;--vscode-editorSuggestWidget-background: #252526;--vscode-editorSuggestWidget-border: #454545;--vscode-editorSuggestWidget-foreground: #d4d4d4;--vscode-editorSuggestWidget-selectedForeground: #ffffff;--vscode-editorSuggestWidget-selectedIconForeground: #ffffff;--vscode-editorSuggestWidget-selectedBackground: #04395e;--vscode-editorSuggestWidget-highlightForeground: #2aaaff;--vscode-editorSuggestWidget-focusHighlightForeground: #2aaaff;--vscode-editorSuggestWidgetStatus-foreground: rgba(212, 212, 212, .5);--vscode-tab-activeBackground: #1e1e1e;--vscode-tab-unfocusedActiveBackground: #1e1e1e;--vscode-tab-inactiveBackground: #2d2d2d;--vscode-tab-unfocusedInactiveBackground: #2d2d2d;--vscode-tab-activeForeground: #ffffff;--vscode-tab-inactiveForeground: rgba(255, 255, 255, .5);--vscode-tab-unfocusedActiveForeground: rgba(255, 255, 255, .5);--vscode-tab-unfocusedInactiveForeground: rgba(255, 255, 255, .25);--vscode-tab-border: #252526;--vscode-tab-lastPinnedBorder: rgba(204, 204, 204, .2);--vscode-tab-activeModifiedBorder: #3399cc;--vscode-tab-inactiveModifiedBorder: rgba(51, 153, 204, .5);--vscode-tab-unfocusedActiveModifiedBorder: rgba(51, 153, 204, .5);--vscode-tab-unfocusedInactiveModifiedBorder: rgba(51, 153, 204, .25);--vscode-editorPane-background: #1e1e1e;--vscode-editorGroupHeader-tabsBackground: #252526;--vscode-editorGroupHeader-noTabsBackground: #1e1e1e;--vscode-editorGroup-border: #444444;--vscode-editorGroup-dropBackground: rgba(83, 89, 93, .5);--vscode-editorGroup-dropIntoPromptForeground: #cccccc;--vscode-editorGroup-dropIntoPromptBackground: #252526;--vscode-sideBySideEditor-horizontalBorder: #444444;--vscode-sideBySideEditor-verticalBorder: #444444;--vscode-panel-background: #1e1e1e;--vscode-panel-border: rgba(128, 128, 128, .35);--vscode-panelTitle-activeForeground: #e7e7e7;--vscode-panelTitle-inactiveForeground: rgba(231, 231, 231, .6);--vscode-panelTitle-activeBorder: #e7e7e7;--vscode-panel-dropBorder: #e7e7e7;--vscode-panelSection-dropBackground: rgba(83, 89, 93, .5);--vscode-panelSectionHeader-background: rgba(128, 128, 128, .2);--vscode-panelSection-border: rgba(128, 128, 128, .35);--vscode-banner-background: #04395e;--vscode-banner-foreground: #ffffff;--vscode-banner-iconForeground: #3794ff;--vscode-statusBar-foreground: #ffffff;--vscode-statusBar-noFolderForeground: #ffffff;--vscode-statusBar-background: #007acc;--vscode-statusBar-noFolderBackground: #68217a;--vscode-statusBar-focusBorder: #ffffff;--vscode-statusBarItem-activeBackground: rgba(255, 255, 255, .18);--vscode-statusBarItem-focusBorder: #ffffff;--vscode-statusBarItem-hoverBackground: rgba(255, 255, 255, .12);--vscode-statusBarItem-compactHoverBackground: rgba(255, 255, 255, .2);--vscode-statusBarItem-prominentForeground: #ffffff;--vscode-statusBarItem-prominentBackground: rgba(0, 0, 0, .5);--vscode-statusBarItem-prominentHoverBackground: rgba(0, 0, 0, .3);--vscode-statusBarItem-errorBackground: #c72e0f;--vscode-statusBarItem-errorForeground: #ffffff;--vscode-statusBarItem-warningBackground: #7a6400;--vscode-statusBarItem-warningForeground: #ffffff;--vscode-activityBar-background: #333333;--vscode-activityBar-foreground: #ffffff;--vscode-activityBar-inactiveForeground: rgba(255, 255, 255, .4);--vscode-activityBar-activeBorder: #ffffff;--vscode-activityBar-dropBorder: #ffffff;--vscode-activityBarBadge-background: #007acc;--vscode-activityBarBadge-foreground: #ffffff;--vscode-statusBarItem-remoteBackground: #16825d;--vscode-statusBarItem-remoteForeground: #ffffff;--vscode-extensionBadge-remoteBackground: #007acc;--vscode-extensionBadge-remoteForeground: #ffffff;--vscode-sideBar-background: #252526;--vscode-sideBarTitle-foreground: #bbbbbb;--vscode-sideBar-dropBackground: rgba(83, 89, 93, .5);--vscode-sideBarSectionHeader-background: rgba(0, 0, 0, 0);--vscode-sideBarSectionHeader-border: rgba(204, 204, 204, .2);--vscode-titleBar-activeForeground: #cccccc;--vscode-titleBar-inactiveForeground: rgba(204, 204, 204, .6);--vscode-titleBar-activeBackground: #3c3c3c;--vscode-titleBar-inactiveBackground: rgba(60, 60, 60, .6);--vscode-menubar-selectionForeground: #cccccc;--vscode-menubar-selectionBackground: rgba(90, 93, 94, .31);--vscode-notifications-foreground: #cccccc;--vscode-notifications-background: #252526;--vscode-notificationLink-foreground: #3794ff;--vscode-notificationCenterHeader-background: #303031;--vscode-notifications-border: #303031;--vscode-notificationsErrorIcon-foreground: #f14c4c;--vscode-notificationsWarningIcon-foreground: #cca700;--vscode-notificationsInfoIcon-foreground: #3794ff;--vscode-commandCenter-foreground: #cccccc;--vscode-commandCenter-activeForeground: #cccccc;--vscode-commandCenter-activeBackground: rgba(90, 93, 94, .31);--vscode-commandCenter-border: rgba(128, 128, 128, .35);--vscode-editorCommentsWidget-resolvedBorder: rgba(204, 204, 204, .5);--vscode-editorCommentsWidget-unresolvedBorder: #3794ff;--vscode-editorCommentsWidget-rangeBackground: rgba(55, 148, 255, .1);--vscode-editorCommentsWidget-rangeBorder: rgba(55, 148, 255, .4);--vscode-editorCommentsWidget-rangeActiveBackground: rgba(55, 148, 255, .1);--vscode-editorCommentsWidget-rangeActiveBorder: rgba(55, 148, 255, .4);--vscode-editorGutter-commentRangeForeground: #37373d;--vscode-debugToolBar-background: #333333;--vscode-debugIcon-startForeground: #89d185;--vscode-editor-stackFrameHighlightBackground: rgba(255, 255, 0, .2);--vscode-editor-focusedStackFrameHighlightBackground: rgba(122, 189, 122, .3);--vscode-mergeEditor-change\.background: rgba(155, 185, 85, .2);--vscode-mergeEditor-change\.word\.background: rgba(156, 204, 44, .2);--vscode-mergeEditor-conflict\.unhandledUnfocused\.border: rgba(255, 166, 0, .48);--vscode-mergeEditor-conflict\.unhandledFocused\.border: #ffa600;--vscode-mergeEditor-conflict\.handledUnfocused\.border: rgba(134, 134, 134, .29);--vscode-mergeEditor-conflict\.handledFocused\.border: rgba(193, 193, 193, .8);--vscode-mergeEditor-conflict\.handled\.minimapOverViewRuler: rgba(173, 172, 168, .93);--vscode-mergeEditor-conflict\.unhandled\.minimapOverViewRuler: #fcba03;--vscode-mergeEditor-conflictingLines\.background: rgba(255, 234, 0, .28);--vscode-settings-headerForeground: #e7e7e7;--vscode-settings-modifiedItemIndicator: #0c7d9d;--vscode-settings-headerBorder: rgba(128, 128, 128, .35);--vscode-settings-sashBorder: rgba(128, 128, 128, .35);--vscode-settings-dropdownBackground: #3c3c3c;--vscode-settings-dropdownForeground: #f0f0f0;--vscode-settings-dropdownBorder: #3c3c3c;--vscode-settings-dropdownListBorder: #454545;--vscode-settings-checkboxBackground: #3c3c3c;--vscode-settings-checkboxForeground: #f0f0f0;--vscode-settings-checkboxBorder: #3c3c3c;--vscode-settings-textInputBackground: #3c3c3c;--vscode-settings-textInputForeground: #cccccc;--vscode-settings-numberInputBackground: #3c3c3c;--vscode-settings-numberInputForeground: #cccccc;--vscode-settings-focusedRowBackground: rgba(42, 45, 46, .6);--vscode-settings-rowHoverBackground: rgba(42, 45, 46, .3);--vscode-settings-focusedRowBorder: rgba(255, 255, 255, .12);--vscode-terminal-foreground: #cccccc;--vscode-terminal-selectionBackground: #264f78;--vscode-terminal-inactiveSelectionBackground: #3a3d41;--vscode-terminalCommandDecoration-defaultBackground: rgba(255, 255, 255, .25);--vscode-terminalCommandDecoration-successBackground: #1b81a8;--vscode-terminalCommandDecoration-errorBackground: #f14c4c;--vscode-terminalOverviewRuler-cursorForeground: rgba(160, 160, 160, .8);--vscode-terminal-border: rgba(128, 128, 128, .35);--vscode-terminal-findMatchBackground: #515c6a;--vscode-terminal-findMatchHighlightBackground: rgba(234, 92, 0, .33);--vscode-terminalOverviewRuler-findMatchForeground: rgba(209, 134, 22, .49);--vscode-terminal-dropBackground: rgba(83, 89, 93, .5);--vscode-testing-iconFailed: #f14c4c;--vscode-testing-iconErrored: #f14c4c;--vscode-testing-iconPassed: #73c991;--vscode-testing-runAction: #73c991;--vscode-testing-iconQueued: #cca700;--vscode-testing-iconUnset: #848484;--vscode-testing-iconSkipped: #848484;--vscode-testing-peekBorder: #f14c4c;--vscode-testing-peekHeaderBackground: rgba(241, 76, 76, .1);--vscode-testing-message\.error\.decorationForeground: #f14c4c;--vscode-testing-message\.error\.lineBackground: rgba(255, 0, 0, .2);--vscode-testing-message\.info\.decorationForeground: rgba(212, 212, 212, .5);--vscode-welcomePage-tileBackground: #252526;--vscode-welcomePage-tileHoverBackground: #2c2c2d;--vscode-welcomePage-tileShadow: rgba(0, 0, 0, .36);--vscode-welcomePage-progress\.background: #3c3c3c;--vscode-welcomePage-progress\.foreground: #3794ff;--vscode-debugExceptionWidget-border: #a31515;--vscode-debugExceptionWidget-background: #420b0d;--vscode-ports-iconRunningProcessForeground: #369432;--vscode-statusBar-debuggingBackground: #cc6633;--vscode-statusBar-debuggingForeground: #ffffff;--vscode-editor-inlineValuesForeground: rgba(255, 255, 255, .5);--vscode-editor-inlineValuesBackground: rgba(255, 200, 0, .2);--vscode-editorGutter-modifiedBackground: #1b81a8;--vscode-editorGutter-addedBackground: #487e02;--vscode-editorGutter-deletedBackground: #f14c4c;--vscode-minimapGutter-modifiedBackground: #1b81a8;--vscode-minimapGutter-addedBackground: #487e02;--vscode-minimapGutter-deletedBackground: #f14c4c;--vscode-editorOverviewRuler-modifiedForeground: rgba(27, 129, 168, .6);--vscode-editorOverviewRuler-addedForeground: rgba(72, 126, 2, .6);--vscode-editorOverviewRuler-deletedForeground: rgba(241, 76, 76, .6);--vscode-debugIcon-breakpointForeground: #e51400;--vscode-debugIcon-breakpointDisabledForeground: #848484;--vscode-debugIcon-breakpointUnverifiedForeground: #848484;--vscode-debugIcon-breakpointCurrentStackframeForeground: #ffcc00;--vscode-debugIcon-breakpointStackframeForeground: #89d185;--vscode-notebook-cellBorderColor: #37373d;--vscode-notebook-focusedEditorBorder: #007fd4;--vscode-notebookStatusSuccessIcon-foreground: #89d185;--vscode-notebookStatusErrorIcon-foreground: #f48771;--vscode-notebookStatusRunningIcon-foreground: #cccccc;--vscode-notebook-cellToolbarSeparator: rgba(128, 128, 128, .35);--vscode-notebook-selectedCellBackground: #37373d;--vscode-notebook-selectedCellBorder: #37373d;--vscode-notebook-focusedCellBorder: #007fd4;--vscode-notebook-inactiveFocusedCellBorder: #37373d;--vscode-notebook-cellStatusBarItemHoverBackground: rgba(255, 255, 255, .15);--vscode-notebook-cellInsertionIndicator: #007fd4;--vscode-notebookScrollbarSlider-background: rgba(121, 121, 121, .4);--vscode-notebookScrollbarSlider-hoverBackground: rgba(100, 100, 100, .7);--vscode-notebookScrollbarSlider-activeBackground: rgba(191, 191, 191, .4);--vscode-notebook-symbolHighlightBackground: rgba(255, 255, 255, .04);--vscode-notebook-cellEditorBackground: #252526;--vscode-notebook-editorBackground: #1e1e1e;--vscode-keybindingTable-headerBackground: rgba(204, 204, 204, .04);--vscode-keybindingTable-rowsBackground: rgba(204, 204, 204, .04);--vscode-scm-providerBorder: #454545;--vscode-debugTokenExpression-name: #c586c0;--vscode-debugTokenExpression-value: rgba(204, 204, 204, .6);--vscode-debugTokenExpression-string: #ce9178;--vscode-debugTokenExpression-boolean: #4e94ce;--vscode-debugTokenExpression-number: #b5cea8;--vscode-debugTokenExpression-error: #f48771;--vscode-debugView-exceptionLabelForeground: #cccccc;--vscode-debugView-exceptionLabelBackground: #6c2022;--vscode-debugView-stateLabelForeground: #cccccc;--vscode-debugView-stateLabelBackground: rgba(136, 136, 136, .27);--vscode-debugView-valueChangedHighlight: #569cd6;--vscode-debugConsole-infoForeground: #3794ff;--vscode-debugConsole-warningForeground: #cca700;--vscode-debugConsole-errorForeground: #f48771;--vscode-debugConsole-sourceForeground: #cccccc;--vscode-debugConsoleInputIcon-foreground: #cccccc;--vscode-debugIcon-pauseForeground: #75beff;--vscode-debugIcon-stopForeground: #f48771;--vscode-debugIcon-disconnectForeground: #f48771;--vscode-debugIcon-restartForeground: #89d185;--vscode-debugIcon-stepOverForeground: #75beff;--vscode-debugIcon-stepIntoForeground: #75beff;--vscode-debugIcon-stepOutForeground: #75beff;--vscode-debugIcon-continueForeground: #75beff;--vscode-debugIcon-stepBackForeground: #75beff;--vscode-extensionButton-prominentBackground: #0e639c;--vscode-extensionButton-prominentForeground: #ffffff;--vscode-extensionButton-prominentHoverBackground: #1177bb;--vscode-extensionIcon-starForeground: #ff8e00;--vscode-extensionIcon-verifiedForeground: #3794ff;--vscode-extensionIcon-preReleaseForeground: #1d9271;--vscode-extensionIcon-sponsorForeground: #d758b3;--vscode-terminal-ansiBlack: #000000;--vscode-terminal-ansiRed: #cd3131;--vscode-terminal-ansiGreen: #0dbc79;--vscode-terminal-ansiYellow: #e5e510;--vscode-terminal-ansiBlue: #2472c8;--vscode-terminal-ansiMagenta: #bc3fbc;--vscode-terminal-ansiCyan: #11a8cd;--vscode-terminal-ansiWhite: #e5e5e5;--vscode-terminal-ansiBrightBlack: #666666;--vscode-terminal-ansiBrightRed: #f14c4c;--vscode-terminal-ansiBrightGreen: #23d18b;--vscode-terminal-ansiBrightYellow: #f5f543;--vscode-terminal-ansiBrightBlue: #3b8eea;--vscode-terminal-ansiBrightMagenta: #d670d6;--vscode-terminal-ansiBrightCyan: #29b8db;--vscode-terminal-ansiBrightWhite: #e5e5e5;--vscode-interactive-activeCodeBorder: #3794ff;--vscode-interactive-inactiveCodeBorder: #37373d;--vscode-gitDecoration-addedResourceForeground: #81b88b;--vscode-gitDecoration-modifiedResourceForeground: #e2c08d;--vscode-gitDecoration-deletedResourceForeground: #c74e39;--vscode-gitDecoration-renamedResourceForeground: #73c991;--vscode-gitDecoration-untrackedResourceForeground: #73c991;--vscode-gitDecoration-ignoredResourceForeground: #8c8c8c;--vscode-gitDecoration-stageModifiedResourceForeground: #e2c08d;--vscode-gitDecoration-stageDeletedResourceForeground: #c74e39;--vscode-gitDecoration-conflictingResourceForeground: #e4676b;--vscode-gitDecoration-submoduleResourceForeground: #8db9e2}.cm-wrapper{line-height:18px}.cm-wrapper,.cm-wrapper>div{width:100%;height:100%}.CodeMirror span.cm-meta{color:var(--vscode-editor-foreground)}.CodeMirror span.cm-number{color:var(--vscode-debugTokenExpression-number)}.CodeMirror span.cm-keyword,.CodeMirror span.cm-builtin{color:var(--vscode-debugTokenExpression-name)}.CodeMirror span.cm-operator{color:var(--vscode-editor-foreground)}.CodeMirror span.cm-string,.CodeMirror span.cm-string-2{color:var(--vscode-debugTokenExpression-string)}.CodeMirror span.cm-error{color:var(--vscode-errorForeground)}.CodeMirror span.cm-def,.CodeMirror span.cm-tag{color:#0070c1}.CodeMirror span.cm-comment,.CodeMirror span.cm-link{color:green}.CodeMirror span.cm-variable,.CodeMirror span.cm-variable-2,.CodeMirror span.cm-atom{color:#0070c1}.CodeMirror span.cm-property{color:#795e26}.CodeMirror span.cm-qualifier,.CodeMirror span.cm-attribute{color:#001080}.CodeMirror span.cm-variable-3,.CodeMirror span.cm-type{color:#267f99}body.dark-mode .CodeMirror span.cm-def,body.dark-mode .CodeMirror span.cm-tag{color:var(--vscode-debugView-valueChangedHighlight)}body.dark-mode .CodeMirror span.cm-comment,body.dark-mode .CodeMirror span.cm-link{color:#6a9955}body.dark-mode .CodeMirror span.cm-variable,body.dark-mode .CodeMirror span.cm-variable-2,body.dark-mode .CodeMirror span.cm-atom{color:#4fc1ff}body.dark-mode .CodeMirror span.cm-property{color:#dcdcaa}body.dark-mode .CodeMirror span.cm-qualifier,body.dark-mode .CodeMirror span.cm-attribute{color:#9cdcfe}body.dark-mode .CodeMirror span.cm-variable-3,body.dark-mode .CodeMirror span.cm-type{color:#4ec9b0}.CodeMirror span.cm-bracket{color:var(--vscode-editorBracketHighlight-foreground3)}.CodeMirror-cursor{border-left:1px solid var(--vscode-editor-foreground)!important}.CodeMirror div.CodeMirror-selected{background:var(--vscode-terminal-inactiveSelectionBackground)}.CodeMirror .CodeMirror-gutters{z-index:0;background:1px solid var(--vscode-editorGroup-border);border-right:none}.CodeMirror .CodeMirror-gutter-elt{background-color:var(--vscode-editorGutter-background)}.CodeMirror .CodeMirror-gutterwrapper{border-right:1px solid var(--vscode-editorGroup-border);color:var(--vscode-editorLineNumber-foreground)}.CodeMirror .CodeMirror-matchingbracket{background-color:var(--vscode-editorBracketPairGuide-background1);color:var(--vscode-editorBracketHighlight-foreground1)!important}.CodeMirror{font-family:var(--vscode-editor-font-family)!important;color:var(--vscode-editor-foreground)!important;background-color:var(--vscode-editor-background)!important;font-weight:var(--vscode-editor-font-weight)!important;font-size:var(--vscode-editor-font-size)!important}.CodeMirror .source-line-running{background-color:var(--vscode-editor-selectionBackground);z-index:2}.CodeMirror .source-line-paused{background-color:var(--vscode-editor-selectionHighlightBackground);z-index:2}.CodeMirror .source-line-error-widget{background-color:var(--vscode-inputValidation-errorBackground);white-space:pre-wrap;margin:3px 10px;padding:5px}.CodeMirror span.cm-link,span.cm-linkified{color:var(--vscode-textLink-foreground);text-decoration:underline;cursor:pointer}.CodeMirror .source-line-error-underline{text-decoration:underline;text-decoration-color:var(--vscode-errorForeground);text-decoration-style:wavy}.CodeMirror-placeholder{color:var(--vscode-input-placeholderForeground)!important}.expandable{flex:none;flex-direction:column;line-height:28px}.expandable-title{flex:none;display:flex;flex-direction:row;align-items:center;white-space:nowrap;-webkit-user-select:none;user-select:none;cursor:pointer}.source-tab{flex:auto;position:relative;overflow:hidden;display:flex;flex-direction:row}.source-tab-file-name{padding-left:8px;height:100%;display:flex;align-items:center;flex:1 1 auto}.source-tab-file-name div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.stack-trace-frame-function{flex:1 1 100px;overflow:hidden;text-overflow:ellipsis}.stack-trace-frame-location{flex:1 1 100px;overflow:hidden;text-overflow:ellipsis;text-align:end}.stack-trace-frame-line{flex:none}.toolbar{position:relative;display:flex;color:var(--vscode-sideBarTitle-foreground);min-height:35px;align-items:center;flex:none;padding-right:4px}.toolbar.toolbar-sidebar-background{background-color:var(--vscode-sideBar-background)}.toolbar:after{content:"";display:block;position:absolute;pointer-events:none;top:0;bottom:0;left:-2px;right:-2px;box-shadow:var(--vscode-scrollbar-shadow) 0 6px 6px -6px;z-index:100}.toolbar.no-shadow:after{box-shadow:none}.toolbar.no-min-height{min-height:0}.toolbar input{padding:0 5px;line-height:24px;outline:none;margin:0 4px}.toolbar select{background:none;outline:none;padding:3px;margin:2px}.toolbar option{background-color:var(--vscode-tab-activeBackground)}.toolbar input,.toolbar select{border:none;color:var(--vscode-input-foreground);background-color:var(--vscode-input-background)}.console-tab{display:flex;flex:auto;white-space:pre}.console-line{width:100%;-webkit-user-select:text;user-select:text}.console-line .codicon{padding:0 2px 0 3px;position:relative;flex:none;top:3px}.console-line.warning .codicon{color:#ff8c00}.console-line-message{word-break:break-word;white-space:pre-wrap;position:relative}.console-location{padding-right:3px;float:right;color:var(--vscode-editorCodeLens-foreground);-webkit-user-select:none;user-select:none}.console-time{float:left;min-width:50px;color:var(--vscode-editorCodeLens-foreground);-webkit-user-select:none;user-select:none}.console-stack{white-space:pre-wrap;margin-left:50px}.console-line .codicon.status-none:after,.console-line .codicon.status-error:after,.console-line .codicon.status-warning:after{display:inline-block;content:"a";color:transparent;border-radius:4px;width:8px;height:8px;position:relative;top:8px;left:-7px}.console-line .codicon.status-error:after{background-color:var(--vscode-errorForeground)}.console-line .codicon.status-warning:after{background-color:var(--vscode-list-warningForeground)}.console-repeat{display:inline-block;padding:0 2px;font-size:12px;line-height:18px;border-radius:6px;background-color:#8c959f;color:#fff;margin-right:10px;flex:none;font-weight:600}.network-request-status-route{color:var(--vscode-statusBar-foreground);background-color:var(--vscode-statusBar-background)}.network-request-status-route.api{color:var(--vscode-statusBar-foreground);background-color:var(--vscode-statusBarItem-remoteBackground)}.network-grid-view .grid-view-column-method,.network-grid-view .grid-view-column-status{text-align:center}.network-grid-view .grid-view-column-duration,.network-grid-view .grid-view-column-size,.network-grid-view .grid-view-column-start{text-align:end}.network-request-details-tab{width:100%;height:100%;-webkit-user-select:text;user-select:text;line-height:24px;margin-left:10px;overflow:auto}.network-request-details-url{white-space:normal;word-wrap:break-word;margin-left:10px}.network-request-details-headers{white-space:pre;overflow:hidden;margin-left:10px}.network-request-details-header{margin:3px 0;font-weight:700}.network-request-details-general{white-space:pre;margin-left:10px}.network-request-details-tab .cm-wrapper{overflow:hidden}.network-font-preview{font-family:font-preview;font-size:30px;line-height:40px;padding:16px 16px 16px 6px;overflow:hidden;text-overflow:ellipsis;text-align:center}.network-font-preview-error{margin-top:8px;text-align:center}.tab-network .toolbar{min-height:30px!important;background-color:initial!important;border-bottom:1px solid var(--vscode-panel-border)}.tab-network .toolbar:after{box-shadow:none!important}.tab-network .tabbed-pane-tab.selected{font-weight:700}.copy-request-dropdown .copy-request-dropdown-toggle{margin-right:14px;width:135px}.copy-request-dropdown:not(:hover) .copy-request-dropdown-menu{display:none}.copy-request-dropdown .copy-request-dropdown-menu{position:absolute;display:flex;flex-direction:column;width:135px;z-index:10;background-color:var(--vscode-list-dropBackground)}.copy-request-dropdown .copy-request-dropdown-menu button{padding:8px;text-align:left}.green-circle:before,.red-circle:before,.yellow-circle:before{content:"";display:inline-block;width:12px;height:12px;border-radius:6px;margin-right:2px;align-self:center}.green-circle:before{background-color:var(--vscode-charts-green)}.red-circle:before{background-color:var(--vscode-charts-red)}.yellow-circle:before{background-color:var(--vscode-charts-yellow)}.tabbed-pane{display:flex;flex:auto;overflow:hidden}.tabbed-pane .toolbar{background-color:var(--vscode-sideBar-background)}.tabbed-pane .tab-content{display:flex;flex:auto;overflow:hidden;position:relative;flex-direction:column}.tabbed-pane-tab{padding:2px 6px 0;cursor:pointer;display:flex;align-items:center;justify-content:center;-webkit-user-select:none;user-select:none;border-bottom:2px solid transparent;outline:none;height:100%}.tabbed-pane-tab-label{max-width:250px;white-space:pre;overflow:hidden;text-overflow:ellipsis;display:inline-block}.tabbed-pane-tab.selected{background-color:var(--vscode-tab-activeBackground)}.tabbed-pane-tab-counter{padding:0 4px;background:var(--vscode-menu-separatorBackground);border-radius:8px;height:16px;margin-left:4px;line-height:16px;min-width:18px;display:flex;align-items:center;justify-content:center}.tabbed-pane-tab-counter.error{background:var(--vscode-list-errorForeground);color:var(--vscode-button-foreground)}.grid-view{display:flex;position:relative;flex:auto}.grid-view .list-view-entry{padding-left:0}.grid-view-cell{overflow:hidden;text-overflow:ellipsis;padding:0 5px;flex:none}.grid-view-header{-webkit-user-select:none;user-select:none;display:flex;flex:0 0 30px;align-items:center;flex-direction:row;border-bottom:1px solid var(--vscode-panel-border)}.grid-view-header .codicon-triangle-up,.grid-view-header .codicon-triangle-down{display:none}.grid-view-header>.filter-positive .codicon-triangle-down{display:initial!important}.grid-view-header>.filter-negative .codicon-triangle-up{display:initial!important}.grid-view-header-cell{flex:none;align-items:center;overflow:hidden;text-overflow:ellipsis;padding-left:10px;cursor:pointer;display:flex;white-space:nowrap}.grid-view-header-cell-title{overflow:hidden;text-overflow:ellipsis;flex:auto}.network-filters{display:flex;gap:16px;background-color:var(--vscode-sideBar-background);padding:4px 8px;min-height:32px}.network-filters input[type=search]{padding:0 5px}.network-filters-resource-types{display:flex;gap:8px;align-items:center}.network-filters-resource-type{cursor:pointer;border-radius:2px;padding:3px 8px;text-align:center;overflow:hidden;text-overflow:ellipsis}.network-filters-resource-type.selected{background-color:var(--vscode-list-inactiveSelectionBackground)}.snapshot-tab{align-items:stretch;outline:none;overflow:hidden}.snapshot-tab .toolbar{background-color:var(--vscode-sideBar-background)}.snapshot-tab .toolbar .pick-locator{margin:0 4px}.snapshot-controls{flex:none;background-color:var(--vscode-sideBar-background);color:var(--vscode-sideBarTitle-foreground);display:flex;box-shadow:var(--box-shadow);height:32px;align-items:center;justify-content:center}.snapshot-toggle{margin-top:4px;padding:4px 8px;cursor:pointer;border-radius:20px;margin-left:4px;width:60px;display:flex;align-items:center;justify-content:center}.snapshot-toggle:hover{background-color:#ededed}.snapshot-toggle.toggled{background:var(--gray);color:#fff}.snapshot-tab:focus .snapshot-toggle.toggled{background:var(--vscode-charts-blue)}.snapshot-wrapper{flex:auto;margin:1px;padding:10px;position:relative;--browser-frame-header-height: 40px}.snapshot-container{display:block;box-shadow:0 12px 28px #0003,0 2px 4px #0000001a}.snapshot-switcher{width:100%;height:calc(100% - var(--browser-frame-header-height));position:relative}iframe[name=snapshot]{position:absolute;top:0;left:0;width:100%;height:100%;border:none;background:#fff;visibility:hidden}iframe.snapshot-visible[name=snapshot]{visibility:visible}.no-snapshot{text-align:center;padding:50px}.snapshot-tab .cm-wrapper{line-height:23px;margin-right:4px}.browser-frame-dot{border-radius:50%;display:inline-block;height:12px;margin-right:6px;margin-top:4px;width:12px}.browser-frame-address-bar{background-color:var(--vscode-input-background);border-radius:12.5px;color:var(--vscode-input-foreground);flex:1 0;font:400 16px Arial,sans-serif;margin:0 16px 0 8px;padding:5px 15px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:flex;align-items:center}.browser-frame-address-bar>button{visibility:hidden}.browser-frame-address-bar:hover>button{visibility:visible}.browser-frame-menu-bar{background-color:#aaa;display:block;height:3px;margin:3px 0;width:17px}.browser-frame-header{align-items:center;background:#ebedf0;display:flex;padding:8px 16px;border-top-left-radius:6px;border-top-right-radius:6px;height:var(--browser-frame-header-height)}body.dark-mode .browser-frame-header{background:#444950}.film-strip{flex:none;display:flex;flex-direction:column;position:relative}.film-strip-lanes{flex:none;display:flex;flex-direction:column;position:relative;min-height:50px;max-height:200px;overflow-x:hidden;overflow-y:auto}.film-strip-lane{flex:none;display:flex}.film-strip-frame{flex:none;pointer-events:none;box-shadow:var(--box-shadow)}.film-strip-hover{position:absolute;top:0;left:0;background-color:var(--vscode-panel-background);box-shadow:#0002 0 1.6px 10px,#0000001c 0 .3px 10px;z-index:200;pointer-events:none}.film-strip-hover-title{padding:2px 4px;display:flex;align-items:center;overflow:hidden}.timeline-view{flex:none;position:relative;display:flex;flex-direction:column;padding:20px 0 5px;cursor:text;-webkit-user-select:none;user-select:none;margin-left:10px;overflow-x:clip}.timeline-divider{position:absolute;width:1px;top:0;bottom:0;background-color:var(--vscode-panel-border)}.timeline-time{position:absolute;top:4px;right:3px;font-size:80%;white-space:nowrap;pointer-events:none}.timeline-time-input{cursor:pointer}.timeline-lane{pointer-events:none;overflow:hidden;flex:none;height:20px;position:relative}.timeline-grid{position:absolute;top:0;right:0;bottom:0;left:0}.timeline-bars{position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:none}.timeline-bar{position:absolute;--action-color: gray;--action-background-color: #88888802;border-top:2px solid var(--action-color)}.timeline-bar.active{background-color:var(--action-background-color)}.timeline-bar.action{--action-color: var(--vscode-charts-orange);--action-background-color: #d1861666}.timeline-bar.action.error{--action-color: var(--vscode-charts-red);--action-background-color: #e5140066}.timeline-bar.network{--action-color: var(--vscode-charts-blue);--action-background-color: #1a85ff66}.timeline-bar.console-message{--action-color: var(--vscode-charts-purple);--action-background-color: #1a85ff66}body.dark-mode .timeline-bar.action.error{--action-color: var(--vscode-errorForeground);--action-background-color: #f4877166}.timeline-label{position:absolute;top:0;bottom:0;margin-left:2px;background-color:var(--vscode-panel-background);justify-content:center;display:none;white-space:nowrap}.timeline-label.selected{display:flex}.timeline-marker{display:none;position:absolute;top:0;bottom:0;pointer-events:none;border-left:3px solid var(--light-pink)}.timeline-window{display:flex;position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:none}.timeline-window-center{flex:auto}.timeline-window-drag{height:20px;cursor:grab;pointer-events:all}.timeline-window-curtain{flex:none;background-color:#3879d91a}body.dark-mode .timeline-window-curtain{background-color:#161718bf}.timeline-window-resizer{flex:none;width:10px;cursor:ew-resize;pointer-events:all;position:relative;background-color:var(--vscode-panel-border);border-left:1px solid var(--vscode-panel-border);border-right:1px solid var(--vscode-panel-border)}.annotations-tab{flex:auto;line-height:24px;white-space:pre;overflow:auto;-webkit-user-select:text;user-select:text}.annotation-item{margin:4px 8px;text-wrap:wrap}.workbench-run-status{height:30px;padding:4px;flex:none;display:flex;flex-direction:row;align-items:center;border-bottom:1px solid var(--vscode-panel-border)}.workbench-run-status.failed{color:var(--vscode-errorForeground)}.workbench-run-status .codicon{margin-right:4px}.workbench-run-duration{display:flex;flex:none;align-items:center;color:var(--vscode-editorCodeLens-foreground)}.settings-view{display:flex;flex:none;padding:4px 0;row-gap:8px;-webkit-user-select:none;user-select:none}.settings-view .setting{display:flex;align-items:center}.settings-view .setting label{text-overflow:ellipsis;white-space:nowrap;overflow:hidden;cursor:pointer}.settings-view .setting input{margin-right:5px;flex-shrink:0}
